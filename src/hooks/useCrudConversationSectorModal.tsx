import {
  FormControl,
  FormLabel,
  Input,
  Stack,
  Text,
  useDisclosure,
  useId,
  useToast,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react';
import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  ReactElement,
  useCallback,
  useMemo,
} from 'react';
import CrudModal from '../components/CrudModal';
import AlertDialogBase from '../components/AlertDialog';
import { Controller, ControllerRenderProps, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { ConversationSector, User } from '../types/Prisma';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { apiRoutes } from '../constants/api-routes';
import { colors } from '../constants/colors';
import useGetCrudText from './useGetCrudText';
import { ConversationSectorsService } from '../services/conversation-sectors.service';
import {
  CreateConversationSectorDto,
  UpdateConversationSectorDto,
} from '../types/ConversationSector';
import InputSelect, { SelectOption } from '../components/InputSelect';
import { UsersService } from '../services/users.service';
import { omit } from 'lodash';
import { ExtendedRole } from './useCrudRoleModal';
import { ConversationSectorCategoriesManagement } from '../pages/SettingsPage/ConversationSectorsManagement/ConversationSectorCrudPage/components/ConversationSectorCategoriesManagement';

interface CrudSectorModalContextData {
  openCreateConversationSectorModal: () => void;
  openEditConversationSectorModal: (id: string) => void;
  openDeleteConversationSectorAlert: (id: string) => void;
  currentSectorId: string | null;
}

const CrudSectorModalContext = createContext<
  CrudSectorModalContextData | undefined
>(undefined);

interface CrudSectorModalProviderProps {
  children?: ReactNode;
}

type SectorFormSchema = {
  name: string;
  userIds: string[];
};

const schema = yup.object({
  name: yup.string().required('O nome é obrigatório'),
  userIds: yup.array().of(yup.string()),
});

export function CrudSectorModalProvider({
  children,
}: CrudSectorModalProviderProps): ReactElement {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isAlertOpen,
    onOpen: onOpenAlert,
    onClose: onCloseAlert,
  } = useDisclosure();
  const [sectorId, setSectorId] = useState<string | null>(null);
  const formId = useId();
  const queryClient = useQueryClient();
  const toast = useToast();
  const { crudActionText, crudTitle } = useGetCrudText(!sectorId);
  const isEditing = !!sectorId;
  const [tabIndex, setTabIndex] = useState(0);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    control,
    reset,
  } = useForm<SectorFormSchema>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      userIds: [],
    },
  });

  const openCreateConversationSectorModal = useCallback(() => {
    setSectorId(null);
    setTabIndex(0);
    reset({ name: '', userIds: [] });
    onOpen();
  }, [onOpen, reset]);

  const openEditConversationSectorModal = useCallback(
    (id: string) => {
      setSectorId(id);
      setTabIndex(0);
      onOpen();
    },
    [onOpen],
  );

  const openDeleteConversationSectorAlert = useCallback(
    (id: string) => {
      setSectorId(id);
      onOpenAlert();
    },
    [onOpenAlert],
  );

  const resetState = useCallback(() => {
    setSectorId(null);
    setValue('name', '');
    setValue('userIds', []);
    setTabIndex(0);
    onClose();
    onCloseAlert();
  }, [onClose, onCloseAlert, setValue]);

  useEffect(() => {
    if (!sectorId) return;

    const cached = queryClient.getQueryData<ConversationSector[]>(
      apiRoutes.listConversationSectors(),
    );
    const current = cached?.find((s) => s.id === sectorId);

    if (current) {
      setValue('name', current.name);
    }
  }, [sectorId, queryClient, setValue]);

  const { data: usersOptions = [] } = useQuery(
    apiRoutes.listUsers(),
    async () => {
      const { data } = await UsersService.listUsers();
      return data.map((user: User) => ({
        value: user.id,
        label: `${user.name} (${user.email})`,
      }));
    },
  );

  const createMutation = useMutation(
    (data: CreateConversationSectorDto) =>
      ConversationSectorsService.createConversationSector(data),
    {
      onSuccess: () => {
        toast({
          title: 'Setor criado com sucesso!',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listConversationSectors());
        resetState();
      },
    },
  );

  const updateMutation = useMutation(
    (data: UpdateConversationSectorDto) =>
      ConversationSectorsService.updateConversationSector(sectorId!, data),
    {
      onSuccess: () => {
        toast({
          title: 'Setor atualizado com sucesso!',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listConversationSectors());
        // Don't reset state here - allow user to continue editing if needed
        // Just refetch the data instead
        queryClient.invalidateQueries(
          apiRoutes.getConversationSector(sectorId!),
        );
      },
    },
  );

  const deleteMutation = useMutation(
    (id: string) => ConversationSectorsService.deleteConversationSector(id),
    {
      onSuccess: () => {
        toast({
          title: 'Setor deletado com sucesso!',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        queryClient.invalidateQueries(apiRoutes.listConversationSectors());
        resetState();
      },
    },
  );

  const { data: conversationSectorData } = useQuery(
    [apiRoutes.getConversationSector(sectorId!), sectorId],
    async () => {
      const { data } = await ConversationSectorsService.getConversationSector(
        sectorId!,
      );
      return data;
    },
    {
      enabled: isEditing,
      onSuccess: (data) => {
        setValue('name', data.name || '');

        setValue(
          'userIds',
          data.userConversationSectors?.map((user) => user.userId) || [],
        );
      },
      keepPreviousData: true,
    },
  );

  const handleChangeUser = useCallback(
    (
      value: SelectOption[],
      field: ControllerRenderProps<SectorFormSchema, 'userIds'>,
    ) => {
      const newValues = value.map((item) => item.value);

      const cachedConversationSectors = queryClient.getQueryData<
        ExtendedRole[]
      >(apiRoutes.listConversationSectors());
      const currentRole = cachedConversationSectors?.find(
        (sector) => sector.id === sectorId,
      );

      if (isEditing && currentRole?.users && currentRole.users.length > 0) {
        const currentUserIds = currentRole.users.map(
          (user: { id: string }) => user.id,
        );
        const removedUsers = currentUserIds.filter(
          (id: string) => !newValues.includes(id),
        );

        if (removedUsers.length) {
          toast({
            title: 'Operação não permitida',
            description:
              'Não é possível desalocar um usuário de um setor, apenas realocá-lo para outro setor.',
            status: 'warning',
            duration: 5000,
            isClosable: true,
          });
          return; // Prevent the change
        }
      }

      field.onChange(newValues);
    },
    [isEditing, queryClient, sectorId, toast],
  );

  const onSubmit = useCallback(
    async (data: SectorFormSchema) => {
      try {
        if (!sectorId) {
          await createMutation.mutateAsync(data);
        } else {
          await updateMutation.mutateAsync(data);
        }
      } catch (err) {
        toast({
          title: 'Erro ao salvar setor.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    },
    [createMutation, sectorId, toast, updateMutation],
  );

  const handleTabChange = useCallback((index: number) => {
    setTabIndex(index);
  }, []);

  const renderModalContent = useMemo(() => {
    if (!isEditing) {
      return (
        <form onSubmit={handleSubmit(onSubmit)} id={formId}>
          <Stack spacing={3}>
            <FormControl isRequired>
              <FormLabel>Nome do Setor</FormLabel>
              <Input
                placeholder="Digite o nome do setor"
                {...register('name')}
                isInvalid={!!errors.name}
              />
              <Text color={colors.danger} fontSize="xs">
                {errors.name?.message}
              </Text>
            </FormControl>

            <FormControl>
              <FormLabel>Usuários</FormLabel>
              <Controller
                name="userIds"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    options={usersOptions}
                    isMulti
                    {...omit(field, 'ref')}
                    value={(field.value || [])
                      .filter(
                        (userId: unknown): userId is string =>
                          typeof userId === 'string' && !!userId,
                      )
                      .map(
                        (userId: string) =>
                          usersOptions.find(
                            (option) => option.value === userId,
                          ) || {
                            value: userId,
                            label: userId,
                          },
                      )}
                    onChange={(value: SelectOption[]) =>
                      handleChangeUser(
                        value,
                        field as ControllerRenderProps<
                          SectorFormSchema,
                          'userIds'
                        >,
                      )
                    }
                    placeholder="Selecione os usuários para este cargo"
                  />
                )}
              />
            </FormControl>
          </Stack>
        </form>
      );
    } else {
      return (
        <Tabs index={tabIndex} onChange={handleTabChange} variant="enclosed">
          <TabList>
            <Tab>Configurações do Setor</Tab>
            <Tab>Categorias do Setor</Tab>
          </TabList>
          <TabPanels>
            <TabPanel padding={0} pt={4}>
              <form onSubmit={handleSubmit(onSubmit)} id={formId}>
                <Stack spacing={3}>
                  <FormControl isRequired>
                    <FormLabel>Nome do Setor</FormLabel>
                    <Input
                      placeholder="Digite o nome do setor"
                      {...register('name')}
                      isInvalid={!!errors.name}
                    />
                    <Text color={colors.danger} fontSize="xs">
                      {errors.name?.message}
                    </Text>
                  </FormControl>

                  <FormControl>
                    <FormLabel>Usuários</FormLabel>
                    <Controller
                      name="userIds"
                      control={control}
                      render={({ field }) => (
                        <InputSelect
                          options={usersOptions}
                          isMulti
                          {...omit(field, 'ref')}
                          value={(field.value || [])
                            .filter(
                              (userId: unknown): userId is string =>
                                typeof userId === 'string' && !!userId,
                            )
                            .map(
                              (userId: string) =>
                                usersOptions.find(
                                  (option) => option.value === userId,
                                ) || {
                                  value: userId,
                                  label: userId,
                                },
                            )}
                          onChange={(value: SelectOption[]) =>
                            handleChangeUser(
                              value,
                              field as ControllerRenderProps<
                                SectorFormSchema,
                                'userIds'
                              >,
                            )
                          }
                          placeholder="Selecione os usuários para este cargo"
                        />
                      )}
                    />
                  </FormControl>
                </Stack>
              </form>
            </TabPanel>
            <TabPanel padding={0} pt={4}>
              {isEditing && conversationSectorData && (
                <ConversationSectorCategoriesManagement
                  conversationSector={conversationSectorData}
                />
              )}
            </TabPanel>
          </TabPanels>
        </Tabs>
      );
    }
  }, [
    control,
    errors.name,
    formId,
    handleChangeUser,
    handleSubmit,
    handleTabChange,
    isEditing,
    onSubmit,
    register,
    tabIndex,
    usersOptions,
    conversationSectorData,
  ]);

  const contextValue = useMemo(
    () => ({
      openCreateConversationSectorModal,
      openEditConversationSectorModal,
      openDeleteConversationSectorAlert,
      currentSectorId: sectorId,
    }),
    [
      openCreateConversationSectorModal,
      openEditConversationSectorModal,
      openDeleteConversationSectorAlert,
      sectorId,
    ],
  );

  return (
    <CrudSectorModalContext.Provider value={contextValue}>
      <CrudModal
        isOpen={isOpen}
        onClose={resetState}
        formId={formId}
        title={`${crudTitle} setor`}
        actionButtonText={crudActionText}
        isLoading={false}
        size="xl"
      >
        {renderModalContent}
      </CrudModal>

      <AlertDialogBase
        isOpen={isAlertOpen}
        onClose={resetState}
        title="Deletar setor"
        onConfirm={() => deleteMutation.mutate(sectorId!)}
      >
        Tem certeza que deseja deletar este setor?
      </AlertDialogBase>

      {children}
    </CrudSectorModalContext.Provider>
  );
}

export function useCrudConversationSectorModal(): CrudSectorModalContextData {
  const context = useContext(CrudSectorModalContext);
  if (!context) {
    throw new Error(
      'useCrudConversationSectorModal deve ser usado dentro do CrudSectorModalProvider',
    );
  }
  return context;
}
