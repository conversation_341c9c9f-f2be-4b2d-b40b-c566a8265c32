import { useSearchParams } from 'react-router-dom';
import { CustomerFiltersEnum } from '../types/CustomerFiltersEnum';

function parseStringWithWhiteSpaces(value: string) {
  return value.replace(/\+/g, ' ');
}

export function useCustomerSearchParams() {
  const [searchParams] = useSearchParams();

  const selectedEngagementTemplateIds =
    searchParams.get(CustomerFiltersEnum.SELECTED_ENGAGEMENT_TEMPLATE_IDS) ||
    '';
  const selectedEngagementEmailTemplateIds =
    searchParams.get(
      CustomerFiltersEnum.SELECTED_ENGAGEMENT_EMAIL_TEMPLATE_IDS,
    ) || '';

  const searchQuery = searchParams.get(CustomerFiltersEnum.SEARCH_QUERY) || '';
  const minTotalPurchases =
    searchParams.get(CustomerFiltersEnum.MIN_TOTAL_PURCHASES) || '';
  const maxTotalPurchases =
    searchParams.get(CustomerFiltersEnum.MAX_TOTAL_PURCHASES) || '';
  const minAverageOrderValue =
    searchParams.get(CustomerFiltersEnum.MIN_AVERAGE_ORDER_VALUE) || '';
  const maxAverageOrderValue =
    searchParams.get(CustomerFiltersEnum.MAX_AVERAGE_ORDER_VALUE) || '';
  const minTotalOrders =
    searchParams.get(CustomerFiltersEnum.MIN_TOTAL_ORDERS) || '';
  const maxTotalOrders =
    searchParams.get(CustomerFiltersEnum.MAX_TOTAL_ORDERS) || '';
  const selectedEngagementActionTypes =
    searchParams.get(CustomerFiltersEnum.SELECTED_ENGAGEMENT_ACTION_TYPES) ||
    '';
  const selectedEmailEngagementActionTypes =
    searchParams.get(
      CustomerFiltersEnum.SELECTED_ENGAGEMENT_EMAIL_ACTION_TYPES,
    ) || '';
  const startOrdersCreatedAt =
    searchParams.get(CustomerFiltersEnum.START_ORDERS_CREATED_AT) || '';
  const endOrdersCreatedAt =
    searchParams.get(CustomerFiltersEnum.END_ORDERS_CREATED_AT) || '';
  const minDaysSinceLastCampaign =
    searchParams.get(CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_CAMPAIGN) || '';
  const minDaysSinceLastEmailCampaign =
    searchParams.get(CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN) ||
    '';
  const sortBy = searchParams.get(CustomerFiltersEnum.SORT_BY) || '';
  const minAverageItemValue =
    searchParams.get(CustomerFiltersEnum.MIN_AVERAGE_ITEM_VALUE) || '';
  const maxAverageItemValue =
    searchParams.get(CustomerFiltersEnum.MAX_AVERAGE_ITEM_VALUE) || '';
  const selectedTags =
    searchParams.get(CustomerFiltersEnum.SELECTED_TAGS) || '';
  const excludedTags =
    searchParams.get(CustomerFiltersEnum.EXCLUDED_TAGS) || '';
  const selectedDefaultAgentIds =
    searchParams.get(CustomerFiltersEnum.SELECTED_DEFAULT_AGENT_IDS) || '';
  const isRemarketing =
    searchParams.get(CustomerFiltersEnum.IS_REMARKETING) || '';
  const minDaysSinceLastPurchase =
    searchParams.get(CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PURCHASE) || '';
  const maxDaysSinceLastPurchase =
    searchParams.get(CustomerFiltersEnum.MAX_DAYS_SINCE_LAST_PURCHASE) || '';
  const exactDaysSinceLastPurchase =
    searchParams.get(CustomerFiltersEnum.EXACT_DAYS_SINCE_LAST_PURCHASE) || '';
  const excludedTemplateIds =
    searchParams.get(CustomerFiltersEnum.EXCLUDED_TEMPLATE_IDS) || '';
  const excludedEmailTemplateIds =
    searchParams.get(CustomerFiltersEnum.EXCLUDED_EMAIL_TEMPLATE_IDS) || '';
  const selectedProductIds =
    searchParams.get(CustomerFiltersEnum.SELECTED_PRODUCT_IDS) || '';
  const selectedProductComparator =
    searchParams.get(CustomerFiltersEnum.SELECTED_PRODUCT_COMPARATOR) || '';
  const excludedProductComparator =
    searchParams.get(CustomerFiltersEnum.EXCLUDED_PRODUCT_COMPARATOR) || '';
  const excludedProductIds =
    searchParams.get(CustomerFiltersEnum.EXCLUDED_PRODUCT_IDS) || '';
  const minProductQuantity =
    searchParams.get(CustomerFiltersEnum.MIN_PRODUCT_QUANTITY) || '';
  const maxProductQuantity =
    searchParams.get(CustomerFiltersEnum.MAX_PRODUCT_QUANTITY) || '';
  const minDaysSinceLastProductPurchase =
    searchParams.get(
      CustomerFiltersEnum.MIN_DAYS_SINCE_LAST_PRODUCT_PURCHASE,
    ) || '';
  const maxDaysSinceLastProductPurchase =
    searchParams.get(
      CustomerFiltersEnum.MAX_DAYS_SINCE_LAST_PRODUCT_PURCHASE,
    ) || '';
  const productNameContains =
    searchParams.get(CustomerFiltersEnum.PRODUCT_NAME_CONTAINS) || '';
  const isLastProductPurchased =
    searchParams.get(CustomerFiltersEnum.IS_LAST_PRODUCT_PURCHASED) || '';
  const customFieldId1 =
    searchParams.get(CustomerFiltersEnum.CUSTOM_FIELD_ID_1) || '';
  const customFieldValue1 =
    searchParams.get(CustomerFiltersEnum.CUSTOM_FIELD_VALUE_1) || '';
  const customFieldComparisonType1 =
    searchParams.get(CustomerFiltersEnum.CUSTOM_FIELD_COMPARISON_TYPE_1) || '';
  const isScheduledCampaignsVisible =
    searchParams.get(CustomerFiltersEnum.IS_SCHEDULED_CAMPAIGNS_VISIBLE) || '';
  const isScheduledEmailCampaignsVisible =
    searchParams.get(
      CustomerFiltersEnum.IS_SCHEDULED_EMAIL_CAMPAIGNS_VISIBLE,
    ) || '';
  const platformOrderSource = searchParams.get(
    CustomerFiltersEnum.PLATFORM_ORDER_SOURCE,
  );
  const selectedStates = parseStringWithWhiteSpaces(
    searchParams.get(CustomerFiltersEnum.SELECTED_STATES) || '',
  );
  const selectedCoupons = parseStringWithWhiteSpaces(
    searchParams.get(CustomerFiltersEnum.SELECTED_COUPONS) || '',
  );
  const selectedOrdersStatuses = parseStringWithWhiteSpaces(
    searchParams.get(CustomerFiltersEnum.SELECTED_ORDERS_STATUSES) || '',
  );
  const selectedCities = parseStringWithWhiteSpaces(
    searchParams.get(CustomerFiltersEnum.SELECTED_CITIES) || '',
  );
  const hasEmail = searchParams.get(CustomerFiltersEnum.HAS_EMAIL) || '';
  const selectedCampaignChannel =
    searchParams.get(CustomerFiltersEnum.SELECTED_CAMPAIGN_CHANNEL) || '';
  const daysUntilBirthday =
    searchParams.get(CustomerFiltersEnum.DAYS_UNTIL_BIRTHDAY) || '';

  return {
    selectedEngagementTemplateIds,
    selectedEngagementEmailTemplateIds,
    searchQuery,
    minTotalPurchases,
    maxTotalPurchases,
    minAverageOrderValue,
    maxAverageOrderValue,
    minTotalOrders,
    maxTotalOrders,
    selectedEngagementActionTypes,
    selectedEmailEngagementActionTypes,
    startOrdersCreatedAt,
    endOrdersCreatedAt,
    minDaysSinceLastCampaign,
    minDaysSinceLastEmailCampaign,
    sortBy,
    minAverageItemValue,
    maxAverageItemValue,
    selectedTags,
    excludedTags,
    selectedDefaultAgentIds,
    isRemarketing,
    minDaysSinceLastPurchase,
    maxDaysSinceLastPurchase,
    exactDaysSinceLastPurchase,
    excludedTemplateIds,
    excludedEmailTemplateIds,
    selectedProductIds,
    selectedProductComparator,
    excludedProductComparator,
    excludedProductIds,
    minProductQuantity,
    maxProductQuantity,
    minDaysSinceLastProductPurchase,
    maxDaysSinceLastProductPurchase,
    productNameContains,
    isLastProductPurchased,
    customFieldId1,
    customFieldValue1,
    customFieldComparisonType1,
    isScheduledCampaignsVisible,
    isScheduledEmailCampaignsVisible,
    platformOrderSource,
    selectedStates,
    selectedCoupons,
    selectedOrdersStatuses,
    selectedCities,
    hasEmail,
    selectedCampaignChannel,
    daysUntilBirthday,
  };
}
