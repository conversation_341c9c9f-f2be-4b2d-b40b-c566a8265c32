import {
  Box,
  Container,
  Heading,
  Spinner,
  Text,
  useToast,
  VStack,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { LuCheckCircle2 } from 'react-icons/lu';
import { useMutation, useQueryClient } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import {
  CustomerNameAndCompanyName,
  CustomersService,
} from '../../services/customers.service';

export function EmailOptOutPage() {
  const { emailOptOutId } = useParams<{ emailOptOutId: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [customerNameAndCompanyName, setCustomerNameAndCompanyName] =
    useState<CustomerNameAndCompanyName | null>(null);

  useEffect(() => {
    if (!emailOptOutId) {
      navigate('/');
    }
  }, [emailOptOutId, navigate]);

  const emailOptOutMutation = useMutation(
    async ({ customerId }: { customerId: string }) => {
      const data = await CustomersService.emailOptOut(customerId);
      return data;
    },
    {
      onSuccess: (data) => {
        setCustomerNameAndCompanyName(data);
        setIsLoading(false);
      },
    },
  );

  useEffect(() => {
    if (emailOptOutId) {
      emailOptOutMutation.mutate({ customerId: emailOptOutId });
    }
  }, [emailOptOutId]);

  if (isLoading) {
    return (
      <Container centerContent py={20}>
        <Spinner size="xl" />
      </Container>
    );
  }

  return (
    <Box minH="100vh" bg="gray.50">
      <Container maxW="container.md" py={20}>
        <VStack spacing={8} align="center">
          <Heading as="h1" size="xl" color="gray.700" fontWeight="bold">
            {customerNameAndCompanyName?.companyName || ''}
          </Heading>
          <Box
            bg="white"
            p={10}
            borderRadius="xl"
            shadow="xl"
            w="full"
            textAlign="center"
          >
            <VStack spacing={6}>
              <LuCheckCircle2 size={80} color="#48BB78" />
              <Heading size="lg" color="gray.800">
                Inscrição Cancelada
              </Heading>
              <Text fontSize="lg" color="gray.600" maxW="md">
                Você foi removido da nossa lista de emails.
              </Text>
            </VStack>
          </Box>
          <Text color="gray.500" fontSize="sm">
            Se você tiver alguma dúvida, entre em contato conosco
          </Text>
        </VStack>
      </Container>
    </Box>
  );
}
