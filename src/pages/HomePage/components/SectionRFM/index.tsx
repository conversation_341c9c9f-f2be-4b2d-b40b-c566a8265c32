import {
  Card,
  CardBody,
  CardHeader,
  FormControl,
  FormLabel,
  Heading,
  Select,
} from '@chakra-ui/react';
import RFMChart from '../../../../components/RFMChart';
import { maxRfmRecencyOptions } from '../../../CustomersPage/components/FilterSidebar/SectionRFM';
import SectionWrapper from '../SectionWrapper';

interface SectionRFMProps {
  maxRecency: number;
  onChangeMaxRecency: (maxRecency: number) => void;
}

const SectionRFM = ({ maxRecency, onChangeMaxRecency }: SectionRFMProps) => {
  return (
    <SectionWrapper
      title="Sua matriz RFM"
      description="Entenda como seus clientes estão se comportando com base na matriz RFM."
      rightComponent={
        <FormControl display="flex" width="fit-content" alignItems="center">
          <FormLabel>Período máximo</FormLabel>
          <Select
            value={maxRecency}
            width="200px"
            onChange={(e) => onChangeMaxRecency(Number(e.target.value))}
          >
            {maxRfmRecencyOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.name}
              </option>
            ))}
          </Select>
        </FormControl>
      }
    >
      <RFMChart maxRecency={maxRecency} />
    </SectionWrapper>
  );
};

export default SectionRFM;
