import { Controller, UseFormReturn } from 'react-hook-form';
import { SelectOption } from '../../../../../components/InputSelect';
import { useCustomerSearchParams } from '../../../../../hooks/useCustomerSearchParams';
import { CustomersService } from '../../../../../services/customers.service';
import AccordionItemLayout from '../AccordionItemLayout';
import useSelectOptionsQuery from '../../../../../hooks/useSelectOptionsQuery';
import SelectField from '../../../../../components/SelectField/SelectField';
import { CustomerFiltersEnum } from '../../../../../types/CustomerFiltersEnum';
import FormLabel from '../../../../../components/FormLabel';
import { FormControl, Switch } from '@chakra-ui/react';
import { apiRoutes } from '../../../../../constants/api-routes';
import useUpdateSelectEffect from '../../../../../hooks/useUpdateSelectEffect';
import { UsersService } from '../../../../../services/users.service';
import { useQuery } from 'react-query';
import { useEffect } from 'react';
import { User } from '../../../../../types/Prisma';
import { useSelector } from 'react-redux';
import { RootState } from '../../../../../state/store';
import { CommunicationChannelEnum } from '../../../../../types/CommunicationChannelEnum';
import InputNumber from '../../../../../components/InputNumber';
import { colors } from '../../../../../constants/colors';
import { FiUsers } from 'react-icons/fi';

interface SectionCustomersProps {
  useFormReturn: UseFormReturn<any>;
  updateSelectedValues: (args: any) => void;
}

const SectionCustomers = ({
  useFormReturn,
  updateSelectedValues,
}: SectionCustomersProps) => {
  const { control, setValue } = useFormReturn;
  const { communicationChannel, selectedTemplate, selectedEmailTemplate } =
    useSelector((state: RootState) => state.campaignCreation);
  const isCreatingCampaign = !!selectedTemplate || !!selectedEmailTemplate;
  const {
    selectedStates,
    selectedCities,
    hasEmail,
    selectedDefaultAgentIds,
    daysUntilBirthday,
  } = useCustomerSearchParams();

  const defaultStateOptions = useSelectOptionsQuery(
    apiRoutes.listCustomerStates(),
    CustomersService.listCustomerStates,
    'states',
  );

  const defaultCitiesOptions = useSelectOptionsQuery(
    apiRoutes.listCustomerCities(),
    CustomersService.listCustomerCities,
    'cities',
  );

  const { data: agentsOptions = [] } = useQuery(
    apiRoutes.listCompanyAgents(),
    async () => {
      const { data } = await UsersService.listCompanyAgents();
      return data;
    },
    {
      select: (response): SelectOption[] =>
        response.map(
          (agent: User) =>
            ({
              value: agent.id,
              label: agent.name,
            }) as SelectOption,
        ),
    },
  );

  const loadCitiesOptions = async (search: string): Promise<SelectOption[]> => {
    const { data } = await CustomersService.listCustomerCities(search);

    return data.cities.map((city) => ({
      value: city,
      label: city,
    }));
  };

  const loadAgentsOptions = async (search: string): Promise<SelectOption[]> => {
    return agentsOptions.filter((option) =>
      option.label.toLowerCase().includes(search.toLowerCase()),
    );
  };

  useUpdateSelectEffect(
    defaultStateOptions,
    selectedStates,
    CustomerFiltersEnum.SELECTED_STATES,
    updateSelectedValues,
  );
  useUpdateSelectEffect(
    defaultCitiesOptions,
    selectedCities,
    CustomerFiltersEnum.SELECTED_CITIES,
    updateSelectedValues,
  );

  useUpdateSelectEffect(
    agentsOptions,
    selectedDefaultAgentIds,
    CustomerFiltersEnum.SELECTED_DEFAULT_AGENT_IDS,
    updateSelectedValues,
  );

  useEffect(() => {
    setValue('hasEmail', hasEmail);
  }, [hasEmail]);

  useEffect(() => {
    setValue(CustomerFiltersEnum.DAYS_UNTIL_BIRTHDAY, daysUntilBirthday);
  }, [daysUntilBirthday]);

  return (
    <AccordionItemLayout
      title="Clientes"
      icon={<FiUsers size="18px" color={colors.primary} />}
    >
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Considerar apenas clientes que possuem email"
        >
          Apenas clientes com email
        </FormLabel>
        <Controller
          control={control}
          name="hasEmail"
          render={({ field: { onChange, onBlur, value } }) => (
            <Switch
              disabled={
                isCreatingCampaign &&
                communicationChannel === CommunicationChannelEnum.EMAIL
              }
              onChange={(e) => {
                onChange(e.target.checked);
              }}
              onBlur={onBlur}
              isChecked={value}
            />
          )}
        />
      </FormControl>
      <SelectField
        label={'É de um dos estados'}
        tooltip={'Mostrar clientes que são de um dos estados'}
        name={CustomerFiltersEnum.SELECTED_STATES}
        control={control}
        defaultOptions={defaultStateOptions}
      />
      <SelectField
        label={'É de uma das cidades'}
        tooltip={'Mostrar clientes que são de uma das cidades'}
        name={CustomerFiltersEnum.SELECTED_CITIES}
        control={control}
        isAsync={true}
        defaultOptions={defaultCitiesOptions}
        loadOptions={loadCitiesOptions}
      />
      <SelectField
        label={'Atendente padrão'}
        tooltip={'Mostrar clientes com os seguintes atendentes padrão'}
        name={CustomerFiltersEnum.SELECTED_DEFAULT_AGENT_IDS}
        control={control}
        isAsync={true}
        defaultOptions={agentsOptions}
        loadOptions={loadAgentsOptions}
      />
      <FormControl>
        <FormLabel
          size="sm"
          tooltip="Mostrar clientes que fazem aniversário nos próximos X dias"
        >
          Faz aniversário nos próximos:
        </FormLabel>
        <Controller
          name={CustomerFiltersEnum.DAYS_UNTIL_BIRTHDAY}
          control={control}
          render={({ field }) => {
            return (
              <InputNumber
                value={field.value}
                onChange={(value) => field.onChange(value)}
                size="sm"
                rightAddon="dias"
              />
            );
          }}
        />
      </FormControl>
    </AccordionItemLayout>
  );
};

export default SectionCustomers;
