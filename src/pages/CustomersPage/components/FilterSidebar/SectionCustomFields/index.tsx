import { FormControl, Text, Link, VStack, Box, Select } from '@chakra-ui/react';
import { useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { AiOutlineTable } from 'react-icons/ai';
import { useQuery } from 'react-query';
import FormLabel from '../../../../../components/FormLabel';
import InputNumber from '../../../../../components/InputNumber';
import InputSelect from '../../../../../components/InputSelect';
import { apiRoutes } from '../../../../../constants/api-routes';
import { CompaniesService } from '../../../../../services/companies.service';
import {
  CompanyDefinedFieldDataTypeEnum,
  CompanyDefinedFieldTableEnum,
} from '../../../../../types/CompanyDefinedField';
import AccordionItemLayout from '../AccordionItemLayout';
import { useNavigate } from 'react-router-dom';
import { appPaths } from '../../../../../constants/app-paths';

interface SectionCustomFieldsProps {
  useFormReturn: UseFormReturn<any>;
}

enum ComparisonType {
  CONTAINS = 'contains',
  EQUALS = 'equals',
}

const SectionCustomFields = ({ useFormReturn }: SectionCustomFieldsProps) => {
  const {
    register,
    control,
    setValue,
    formState: { errors },
  } = useFormReturn;
  const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
  const [fieldValues, setFieldValues] = useState<
    { value: string; label: string }[]
  >([]);
  const [comparisonType, setComparisonType] = useState<ComparisonType | null>(
    null,
  );

  const { data: companyDefinedFields } = useQuery(
    apiRoutes.listCompanyDefinedFields(CompanyDefinedFieldTableEnum.CUSTOMERS),
    async () => {
      const { data } = await CompaniesService.listCompanyDefinedFields(
        CompanyDefinedFieldTableEnum.CUSTOMERS,
      );
      return data;
    },
  );
  const { data } = useQuery(
    ['customFieldValues', selectedFieldId],
    async () => {
      if (selectedFieldId) {
        const field = companyDefinedFields?.find(
          (field) => field.id === selectedFieldId,
        );
        if (field) {
          const { data } =
            await CompaniesService.listValuesInCompanyDefinedField(field.name);
          return data.map((value: string) => ({
            value,
            label: value,
          }));
        }
      }
      return [];
    },
    {
      enabled: !!selectedFieldId,
      onSuccess: (data) => {
        setFieldValues(data);
      },
    },
  );

  const navigate = useNavigate();
  const selectedField = companyDefinedFields?.find(
    (field) => field.id === selectedFieldId,
  );

  const isDisabled = !companyDefinedFields || companyDefinedFields.length === 0;

  const handleComparisonChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selected = e.target.value as ComparisonType;
    setComparisonType(selected);
    setValue('customFieldComparisonType1', selected);
  };

  const handleCreateCustomFieldValue = (inputValue: string) => {
    if (selectedField?.dataType !== 'number') {
      setValue('customFieldValue1', inputValue, {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  };

  const handleCreateNumberValue = (inputValue: string) => {
    const numValue = parseFloat(inputValue);
    if (!isNaN(numValue)) {
      setValue('customFieldValue1', numValue, {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  };

  return (
    <AccordionItemLayout
      title="Colunas customizadas"
      icon={<AiOutlineTable size="18px" />}
    >
      <VStack spacing={4} align="stretch">
        <FormControl>
          <Box>
            <FormLabel
              size="sm"
              tooltip="Selecione uma coluna customizada para filtrar"
            >
              Nome da coluna
            </FormLabel>
            <Select
              size="md"
              bg="white"
              placeholder="Selecione uma coluna"
              isDisabled={isDisabled}
              {...register('customFieldId1')}
              onChange={(e) => {
                setSelectedFieldId(e.target.value);
                setComparisonType(null);
              }}
            >
              {companyDefinedFields?.map((field) => (
                <option key={field.id} value={field.id}>
                  {field.name}
                </option>
              ))}
            </Select>
          </Box>

          {isDisabled && (
            <Text fontSize="sm" color="gray.500" mt={2}>
              Nenhuma coluna customizada configurada.
              <Link
                color="blue.500"
                onClick={() => navigate(appPaths.settings.customColumns())}
                ml={1}
              >
                Configurar colunas
              </Link>
            </Text>
          )}
        </FormControl>

        {selectedField && (
          <>
            <FormControl isInvalid={!!errors.customFieldComparisonType1}>
              <Box>
                <FormLabel
                  size="sm"
                  tooltip="Escolha como deseja comparar o valor da coluna"
                >
                  Tipo de Comparação
                </FormLabel>
                <Select
                  size="md"
                  bg="white"
                  isDisabled={isDisabled || !selectedFieldId}
                  {...register('customFieldComparisonType1', {
                    required: 'Selecione um tipo de comparação',
                  })}
                  value={comparisonType || ''}
                  onChange={handleComparisonChange}
                  placeholder="Selecione o comparador"
                >
                  <option value={ComparisonType.CONTAINS}>Contém</option>
                  <option value={ComparisonType.EQUALS}>Igual a</option>
                </Select>
                {errors.customFieldComparisonType1 && (
                  <Text fontSize="sm" color="red.500">
                    {errors.customFieldComparisonType1.message}
                  </Text>
                )}
              </Box>
            </FormControl>

            <FormControl isInvalid={!!errors.customFieldValue1}>
              <Box>
                <FormLabel
                  size="sm"
                  tooltip="Insira o valor para filtrar a coluna selecionada"
                >
                  Valor -{' '}
                  {selectedField?.dataType ===
                  CompanyDefinedFieldDataTypeEnum.NUMBER
                    ? 'Número'
                    : 'Texto'}
                </FormLabel>
                {selectedField?.dataType === 'number' ? (
                  <Controller
                    name="customFieldValue1"
                    control={control}
                    rules={{ required: 'Insira um valor' }}
                    render={({ field }) => {
                      return (
                        <InputSelect
                          onCreateOption={handleCreateNumberValue}
                          placeholder="Selecione ou digite um valor para buscar"
                          options={fieldValues.map((item) => ({
                            value: item.value,
                            label: item.label,
                          }))}
                          value={
                            field.value
                              ? [
                                  {
                                    value: field.value,
                                    label: field.value.toString(),
                                  },
                                ]
                              : undefined
                          }
                          onChange={(selectedOption) => {
                            const numberValue = selectedOption
                              ? selectedOption.value
                              : null;
                            field.onChange(numberValue);
                          }}
                          disabled={isDisabled || !comparisonType}
                          createLabel={(inputValue) =>
                            `Setar valor: ${inputValue}`
                          }
                        />
                      );
                    }}
                  />
                ) : (
                  <Controller
                    name="customFieldValue1"
                    control={control}
                    rules={{ required: 'Insira um valor' }}
                    render={({ field }) => (
                      <InputSelect
                        onCreateOption={handleCreateCustomFieldValue}
                        placeholder="Selecione ou digite um valor para buscar"
                        options={fieldValues}
                        value={
                          field.value
                            ? [{ value: field.value, label: field.value }]
                            : undefined
                        }
                        onChange={(selectedOption) => {
                          const stringValue = selectedOption
                            ? selectedOption.value
                            : '';
                          field.onChange(stringValue);
                        }}
                        disabled={isDisabled || !comparisonType}
                        createLabel={(inputValue) =>
                          `Setar valor: ${inputValue}`
                        }
                      />
                    )}
                  />
                )}
                {errors.customFieldValue1 && (
                  <Text fontSize="sm" color="red.500">
                    {errors.customFieldValue1.message}
                  </Text>
                )}
              </Box>
            </FormControl>
          </>
        )}
      </VStack>
    </AccordionItemLayout>
  );
};

export default SectionCustomFields;
