import { useState, useEffect, useRef } from 'react';
import {
  Md<PERSON>eyboardVoice,
  MdSend,
  MdStop,
  MdDelete,
  MdDownload,
  MdClose,
  MdMoreVert,
} from 'react-icons/md';
import {
  ButtonGroup,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  ModalHeader,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
} from '@chakra-ui/react';
import { IoTrash } from 'react-icons/io5';
import { colors } from '../../../../constants/colors';
import './styles/audio-recorder.css';
import { useAudioRecorder } from '../../../../hooks/useAudioRecorder';
import { AudioUtils } from '../../../../utils/audio.utils';

interface InputButtonsProps {
  currentText: string;
  onSend?: (file: File) => void;
  isDesktop: boolean;
  onAudioRecordChange?: (audioUrl?: string, audioExtension?: string) => void;
  hideSendButton?: boolean;
  fileAttached?: boolean;
}

const InputButtons = ({
  currentText,
  onSend,
  isDesktop,
  onAudioRecordChange,
  hideSendButton = false,
  fileAttached,
}: InputButtonsProps) => {
  const audioRecorder = useAudioRecorder();
  const [showAudioModal, setShowAudioModal] = useState(false);
  const menuRef = useRef(null);

  const startRecording = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    audioRecorder.startRecording();
  };

  const stopRecording = () => {
    if (audioRecorder.recorderState === 'recording') {
      audioRecorder.stopRecording();
      if (!isDesktop) {
        setShowAudioModal(true);
      }
    }
  };

  const resetRecording = () => {
    audioRecorder.resetRecorder();
    if (!isDesktop) {
      setShowAudioModal(false);
    }
  };

  const handleSendAudio = () => {
    if (!audioRecorder.audioUrl) {
      return;
    }

    AudioUtils.createFileFromRecordedAudioUrl(
      audioRecorder.audioUrl,
      `audio${audioRecorder.audioExtension}`,
    )
      .then((file) => {
        onSend?.(file);
        resetRecording();
        if (!isDesktop) {
          setShowAudioModal(false);
        }
      })
      .catch((error) => {
        console.error('Erro:', error);
      });
  };

  const handleDownloadAudio = () => {
    if (!audioRecorder.audioUrl) {
      return;
    }

    const link = document.createElement('a');
    link.href = audioRecorder.audioUrl;
    link.download = `audio${audioRecorder.audioExtension}`;
    link.click();
  };

  const closeModal = () => {
    setShowAudioModal(false);
  };

  useEffect(() => {
    onAudioRecordChange?.(
      audioRecorder?.audioUrl || undefined,
      audioRecorder.audioExtension,
    );
  }, [audioRecorder.audioUrl]);

  return (
    <>
      <ButtonGroup
        justifyContent="center"
        alignItems="center"
        size="sm"
        style={{ marginRight: 12 }}
      >
        <div
          style={{
            height: '20px',
          }}
        >
          {isDesktop && audioRecorder.audioUrl ? (
            <div className="audio-container">
              <audio controls src={audioRecorder.audioUrl}></audio>
              <button onClick={resetRecording}>
                <IoTrash fontSize={16} color={colors.danger} />
              </button>
              {!hideSendButton && (
                <button onClick={handleSendAudio}>
                  <MdSend fontSize={16} />
                </button>
              )}
              <Menu placement="top">
                <MenuButton
                  as="button"
                  className="audio-menu-button"
                  ref={menuRef}
                >
                  <MdMoreVert fontSize={16} />
                </MenuButton>
                <MenuList minWidth="120px">
                  <MenuItem
                    onClick={handleDownloadAudio}
                    className="audio-menu-item"
                  >
                    <MdDownload fontSize={16} style={{ marginRight: '8px' }} />
                    Baixar
                  </MenuItem>
                </MenuList>
              </Menu>
            </div>
          ) : audioRecorder.recorderState === 'recording' ? (
            <ButtonGroup justifyContent="center" alignItems="center" size="sm">
              <button onClick={resetRecording}>
                <IoTrash fontSize={16} color={colors.danger} />
              </button>

              <div className="button-stop-container">
                <div className="circle"></div>
                <button
                  onClick={stopRecording}
                  className="button-stop-recording"
                >
                  {<MdStop fontSize={20} color={colors.darkGrey} />}
                </button>
              </div>
            </ButtonGroup>
          ) : (currentText.length > 0 || fileAttached) && !hideSendButton ? (
            <button type="submit">
              <MdSend fontSize={16} color={colors.darkGrey} />
            </button>
          ) : (
            <button onClick={startRecording}>
              {<MdKeyboardVoice fontSize={20} color={colors.darkGrey} />}
            </button>
          )}
        </div>
      </ButtonGroup>

      {!isDesktop && (
        <Modal
          isOpen={showAudioModal}
          onClose={closeModal}
          isCentered
          size="sm"
        >
          <ModalOverlay />
          <ModalContent borderRadius="md">
            <ModalHeader padding="3" className="audio-modal-header">
              <div className="audio-modal-title">Áudio gravado</div>
              <button className="audio-modal-close" onClick={closeModal}>
                <MdClose fontSize={20} />
              </button>
            </ModalHeader>
            <ModalBody padding="4">
              <div className="audio-modal">
                {audioRecorder.audioUrl && (
                  <audio
                    controls
                    src={audioRecorder.audioUrl}
                    className="audio-modal-player"
                  ></audio>
                )}
                <div className="audio-modal-actions">
                  <button
                    className="audio-modal-button"
                    onClick={resetRecording}
                  >
                    <MdDelete fontSize={20} color={colors.danger} />
                    <span>Excluir</span>
                  </button>
                  <button
                    className="audio-modal-button"
                    onClick={handleDownloadAudio}
                  >
                    <MdDownload fontSize={20} color={colors.darkGrey} />
                    <span>Baixar</span>
                  </button>
                  {!hideSendButton && (
                    <button
                      className="audio-modal-button"
                      onClick={handleSendAudio}
                    >
                      <MdSend fontSize={20} color={colors.darkGrey} />
                      <span>Enviar</span>
                    </button>
                  )}
                </div>
              </div>
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </>
  );
};

export default InputButtons;
