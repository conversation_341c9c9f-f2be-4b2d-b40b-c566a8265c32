import {
  Box,
  Button,
  Flex,
  Grid,
  GridItem,
  IconButton,
  Select,
  Tag,
  Text,
  Tooltip,
  useToast,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useSearchParams } from 'react-router-dom';
import { apiRoutes } from '../../../../constants/api-routes';
import { colors } from '../../../../constants/colors';
import { ConversationsService } from '../../../../services/conversations.service';
import ContainerMessages from '../ContainerMessages';
import { IoMdArchive } from 'react-icons/io';
import { GoBlocked } from 'react-icons/go';
import InputArea from './InputArea';
import { useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../../../../state/store';
import {
  deleteMessagesByConversationId,
  getConversationById,
  hideConversation,
  isConversationSessionActive,
  isUnreadConversation,
  readConversation,
  updateConversation,
} from '../../../../state/inboxSlice';
import { useSendTemplateModal } from '../../../../hooks/useSendTemplateModal';
import { FaInfoCircle } from 'react-icons/fa';
import { useDispatch } from 'react-redux';
import { MdKeyboardDoubleArrowLeft } from 'react-icons/md';
import ButtonIcon from '../../../../components/ButtonIcon';
import {
  ConversationTicketsService,
  UpdateConversationTicketDto,
} from '../../../../services/conversation-tickets.service';
import { User } from '../../../../types/Prisma';
import DrawerConversationSettings from '../../../../components/DrawerConversation';
import { TbUserSearch } from 'react-icons/tb';
import { useNavigate } from 'react-router-dom';
import { MessageWithCardsIncludes } from '../../../../types/Message';
import { LuChevronDown } from 'react-icons/lu';
import { FlowsService } from '../../../../services/flows.service';

interface InboxChatProps {
  toggleListConversations: () => void;
  isDesktop: boolean;
  companyAgents: User[];
}

const InboxChat = ({
  toggleListConversations,
  isDesktop,
  companyAgents,
}: InboxChatProps) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const conversationId = searchParams.get('conversationId');
  const { openModal: openSendTemplateModal } = useSendTemplateModal();
  const toast = useToast();
  const selectedConversation = useSelector((state: RootState) =>
    getConversationById(state, conversationId!),
  );
  const queryClient = useQueryClient();
  const { currentUser } = useSelector((state: RootState) => state.auth);
  const agentId = selectedConversation?.ticketAgentId;
  const recipientName = selectedConversation?.recipientName;
  const [contextMessage, setContextMessage] =
    useState<MessageWithCardsIncludes | null>(null);

  const dispatch = useDispatch<AppDispatch>();
  const isUnread = useSelector((state: RootState) =>
    isUnreadConversation(state, conversationId!),
  );
  const isSessionActive = useSelector((state: RootState) =>
    isConversationSessionActive(state, conversationId!),
  );

  const closeAllConversationTickets = useMutation(
    async (triggerCsatFlow: boolean) => {
      const { data } = await ConversationsService.closeAllConversationTickets(
        conversationId!,
        triggerCsatFlow,
      );
      return data;
    },
    {
      onSuccess: () => {
        toggleListConversations();
        searchParams.delete('conversationId');
        setSearchParams(searchParams);
        toast({
          title: 'Conversa movida para "finalizado"',
          status: 'info',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );
  const { data: conversation, refetch } = useQuery(
    apiRoutes.showConversation(conversationId!),
    async () => {
      const { data } = await ConversationsService.showConversation(
        conversationId!,
      );
      return data;
    },
    {
      onSuccess: (data) => {
        if (!data) {
          navigate('/conversas');
        }
      },
      onError: () => {
        navigate('/conversas');
      },
      enabled: !!conversationId,
    },
  );

  const { data: hasActiveCsatFlowData } = useQuery(
    apiRoutes.hasActiveFlow('csat'),
    async () => {
      const { data } = await FlowsService.hasActiveFlow('csat');
      return data;
    },
  );

  const readConversationMutation = useMutation(
    async () => {
      const { data } = await ConversationsService.readConversation(
        conversationId!,
      );
      return data;
    },
    {
      onMutate: () => {
        dispatch(
          readConversation({
            conversationId: conversationId!,
          }),
        );
      },
    },
  );

  useEffect(() => {
    if (conversationId) {
      handleReadConversation();
    }
  }, [conversationId]);

  function handleReadConversation() {
    if (isUnread) {
      readConversationMutation.mutate();
    }
  }

  const updateConversationTicket = useMutation(
    async (updateConversationTicketDto: UpdateConversationTicketDto) => {
      return await ConversationTicketsService.updateConversationTicket(
        updateConversationTicketDto,
      );
    },
    {
      onSuccess: (data) => {
        let toastMessage = 'Responsável pela conversa atualizado.';

        if (data?.data.agentId !== currentUser?.sub) {
          toastMessage +=
            'Outro atendente foi alocado, você não poderá ver essa conversa.';
          queryClient.removeQueries(
            apiRoutes.showConversation(conversationId!),
          );
          navigate('/conversas');
          dispatch(
            hideConversation({
              conversationId: conversationId!,
            }),
          );
          dispatch(deleteMessagesByConversationId(conversationId!));
        }
        toast({
          title: toastMessage,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleSelectAgent(value: string) {
    const { data: updatedConversation } = await refetch();
    const conversationTicket = updatedConversation?.conversationTickets[0];

    if (!conversationTicket) {
      toast({
        title:
          'A conversa não foi inicializada. Envie ao menos uma mensagem para poder alocar um responsável.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    updateConversationTicket.mutate({
      conversationTicketId: conversationTicket.id,
      agentId: value,
    });
  }

  const handleReplyMessage = (message: MessageWithCardsIncludes) => {
    setContextMessage(message);
  };

  const handleClearContextMessage = () => {
    setContextMessage(null);
  };

  return (
    <Grid
      borderWidth={'1px'}
      borderStyle="solid"
      borderColor={colors.lightGrey}
      height="100%"
      width="100%"
      templateRows="60px 1fr auto"
      templateColumns="1fr"
      onClick={() => handleReadConversation()}
    >
      <GridItem
        alignItems="center"
        padding={3}
        borderWidth="1px"
        borderStyle="solid"
        borderColor={colors.lightGrey}
        display="grid"
        gridTemplateColumns={isDesktop ? '1fr auto auto' : 'auto 1fr auto auto'}
        gap={3}
      >
        {conversation && (
          <>
            {!isDesktop && (
              <ButtonIcon
                icon={<MdKeyboardDoubleArrowLeft fontSize="22px" />}
                onClick={toggleListConversations}
              />
            )}
            <Text
              fontWeight="bold"
              fontSize="lg"
              overflow="hidden"
              whiteSpace="nowrap"
              textOverflow="ellipsis"
            >
              {recipientName ||
                conversation?.recipientName ||
                conversation?.recipientPhoneNumberId}
            </Text>

            {conversation?.customer.isOptedOut &&
              (isDesktop ? (
                <Tag size="md" colorScheme="red">
                  Bloqueado
                </Tag>
              ) : (
                <Box>
                  <GoBlocked
                    color="red"
                    fontSize="22px"
                    aria-label="Cliente bloqueado"
                  />
                </Box>
              ))}

            {conversation && (
              <Flex gap={2} alignItems="center">
                {isDesktop && (
                  <Box>
                    <Flex alignItems={'center'} gap={2}>
                      <Text fontSize={12}>Responsável:</Text>
                      <Select
                        value={agentId ?? ''}
                        width="fit-content"
                        onChange={(event: any) => {
                          handleSelectAgent(event.target.value);
                        }}
                        size={'sm'}
                        borderRadius={'md'}
                      >
                        {!agentId ||
                        !companyAgents.find((agent) => agent.id === agentId) ? (
                          <option value="">Ninguém</option>
                        ) : null}
                        {companyAgents.map((agent: User) => (
                          <option key={agent.id} value={agent.id}>
                            {agent.name}
                          </option>
                        ))}
                      </Select>
                    </Flex>
                  </Box>
                )}
                <Box>
                  <DrawerConversationSettings
                    conversationId={conversation.id}
                    customerId={conversation?.customerId ?? ''}
                    trigger={
                      <IconButton
                        aria-label="Detalhes do Cliente"
                        icon={<TbUserSearch />}
                        size="md"
                      />
                    }
                  />
                </Box>
                {conversation?.conversationTickets[0]?.status === 'open' &&
                  (hasActiveCsatFlowData ? (
                    <Menu>
                      <MenuButton
                        as={Button}
                        bgColor="black"
                        color="white"
                        leftIcon={<IoMdArchive />}
                        rightIcon={<LuChevronDown />}
                        isLoading={closeAllConversationTickets.isLoading}
                        iconSpacing={{ base: 0, md: 2 }}
                      >
                        {isDesktop ? 'Finalizar' : null}
                      </MenuButton>
                      <MenuList>
                        <MenuItem
                          onClick={() =>
                            closeAllConversationTickets.mutate(false)
                          }
                        >
                          Finalizar
                        </MenuItem>
                        <MenuItem
                          onClick={() =>
                            closeAllConversationTickets.mutate(true)
                          }
                        >
                          Finalizar com CSAT
                        </MenuItem>
                      </MenuList>
                    </Menu>
                  ) : (
                    <Button
                      bgColor="black"
                      color="white"
                      leftIcon={<IoMdArchive />}
                      onClick={() => closeAllConversationTickets.mutate(false)}
                      isLoading={closeAllConversationTickets.isLoading}
                      iconSpacing={{ base: 0, md: 2 }}
                    >
                      {isDesktop ? 'Finalizar' : null}
                    </Button>
                  ))}
              </Flex>
            )}
          </>
        )}
      </GridItem>
      <GridItem overflow={'auto'}>
        {conversation && (
          <ContainerMessages
            conversationId={conversation.id}
            onReplyMessage={handleReplyMessage}
          />
        )}
      </GridItem>
      <GridItem padding={2} position={'relative'}>
        {conversation &&
          (isSessionActive ? (
            <InputArea
              conversation={conversation!}
              isDesktop={isDesktop}
              contextMessage={contextMessage}
              onClearContextMessage={handleClearContextMessage}
            />
          ) : conversation.id ? (
            <Box
              bgColor={colors.blueLight}
              padding={3}
              borderRadius={'xl'}
              bottom={'8px'}
              maxWidth={'100%'}
            >
              <Flex gap={3}>
                <FaInfoCircle fontSize={'18px'} style={{ marginTop: '4px' }} />
                <Box>
                  <Text fontWeight="bold">
                    Selecione um template de mensagem do whatsapp
                  </Text>
                  <Text>
                    Para continuar, você vai precisar enviar um template de
                    mensagem pré-aprovado. Após o cliente responder, você vai
                    poder continuar a conversa
                  </Text>
                </Box>
              </Flex>

              <Flex justifyContent={'flex-end'}>
                <Tooltip
                  label={
                    conversation?.customer.isOptedOut
                      ? 'Desbloqueie o cliente para poder enviar templates'
                      : null
                  }
                >
                  <Button
                    bgColor="black"
                    color="white"
                    isDisabled={conversation?.customer.isOptedOut}
                    onClick={() =>
                      openSendTemplateModal({
                        conversation: conversation!,
                        templateTypes: [
                          'INITIAL_MESSAGE',
                          'MARKETING',
                          'REVIEW_REQUEST',
                        ],
                        deliveryType: 'direct',
                      })
                    }
                  >
                    Escolher template
                  </Button>
                </Tooltip>
              </Flex>
            </Box>
          ) : null)}
      </GridItem>
    </Grid>
  );
};

export default InboxChat;
