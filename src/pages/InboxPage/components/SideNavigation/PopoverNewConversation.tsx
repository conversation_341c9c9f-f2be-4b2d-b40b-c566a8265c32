import {
  Button,
  ButtonGroup,
  FormControl,
  FormErrorMessage,
  IconButton,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
  Stack,
  useDisclosure,
  useToast,
  useBreakpointValue,
  Drawer,
  DrawerBody,
  DrawerCloseButton,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerOverlay,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FaEdit } from 'react-icons/fa';
import { useSearchParams } from 'react-router-dom';
import PhoneInput from './PhoneInput';
import { useMutation } from 'react-query';
import {
  ConversationsService,
  CreateConversationDto,
} from '../../../../services/conversations.service';

const PopoverNewConversation = ({
  isMobile = false,
  onSidebarClose = () => {},
}) => {
  const [recipientPhoneNumber, setRecipientPhoneNumber] = useState<string>('');
  const [searchParams, setSearchParams] = useSearchParams();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const popover = useDisclosure();
  const drawer = useDisclosure();

  const toast = useToast();

  const useDrawer = useBreakpointValue({ base: true, md: isMobile });

  function resetForm() {
    setErrorMessage('');
    setRecipientPhoneNumber('');
  }

  function handleClose() {
    resetForm();
    if (useDrawer) {
      drawer.onClose();
    } else {
      popover.onClose();
    }
  }

  function handleCreateConversation() {
    createConversation.mutateAsync({
      recipientPhoneNumberId: `${recipientPhoneNumber}`,
    });
  }

  const createConversation = useMutation(
    async (createConversationDto: CreateConversationDto) => {
      const { data } = await ConversationsService.createConverstation(
        createConversationDto,
      );
      return data;
    },
    {
      onSuccess: (data) => {
        toast({
          title: 'Envie uma mensagem para iniciar a conversa',
          status: 'info',
          duration: 3000,
          isClosable: true,
          position: useDrawer ? 'bottom' : 'top',
        });
        drawer.onClose();
        popover.onClose();
        setSearchParams({
          conversationId: data.id,
        });

        if (isMobile) {
          onSidebarClose();
        }
      },
      onError: (err: any) => {
        setErrorMessage(err.response.data.message);
      },
    },
  );

  const triggerButton = (
    <IconButton
      aria-label="Nova Conversa"
      size="sm"
      variant="ghost"
      icon={<FaEdit fontSize={isMobile ? 20 : 16} />}
    />
  );

  if (useDrawer) {
    return (
      <>
        <IconButton
          aria-label="Nova Conversa"
          size="sm"
          variant="ghost"
          icon={<FaEdit fontSize={20} />}
          onClick={drawer.onOpen}
        />

        <Drawer
          isOpen={drawer.isOpen}
          placement="bottom"
          onClose={handleClose}
          size="md"
        >
          <DrawerOverlay />
          <DrawerContent borderTopRadius="md">
            <DrawerCloseButton />
            <DrawerHeader>Nova conversa</DrawerHeader>
            <DrawerBody>
              <FormControl isInvalid={!!errorMessage}>
                <PhoneInput
                  resetPhoneInputData={drawer.isOpen}
                  recipientPhoneNumber={recipientPhoneNumber}
                  setRecipientPhoneNumber={setRecipientPhoneNumber}
                />
                <FormErrorMessage>{errorMessage}</FormErrorMessage>
              </FormControl>
            </DrawerBody>
            <DrawerFooter>
              <ButtonGroup display="flex" justifyContent="flex-end">
                <Button variant="outline" onClick={drawer.onClose}>
                  Cancelar
                </Button>
                <Button
                  onClick={handleCreateConversation}
                  isLoading={createConversation.isLoading}
                >
                  Avançar
                </Button>
              </ButtonGroup>
            </DrawerFooter>
          </DrawerContent>
        </Drawer>
      </>
    );
  }

  return (
    <Popover
      isOpen={popover.isOpen}
      onOpen={popover.onOpen}
      onClose={() => {
        setErrorMessage('');
        popover.onClose();
      }}
      placement="right"
      closeOnBlur={false}
    >
      <PopoverTrigger>{triggerButton}</PopoverTrigger>
      <PopoverContent p={5} width="300px">
        <PopoverHeader fontWeight="semibold">Nova conversa</PopoverHeader>
        <PopoverArrow />
        <PopoverCloseButton />
        <PopoverBody>
          <FormControl isInvalid={!!errorMessage}>
            <PhoneInput
              resetPhoneInputData={popover.isOpen}
              recipientPhoneNumber={recipientPhoneNumber}
              setRecipientPhoneNumber={setRecipientPhoneNumber}
            />
            <FormErrorMessage>{errorMessage}</FormErrorMessage>
          </FormControl>
          <Stack mt={2}>
            <ButtonGroup display="flex" justifyContent="flex-end">
              <Button variant="outline" onClick={popover.onClose}>
                Cancelar
              </Button>
              <Button
                onClick={handleCreateConversation}
                isLoading={createConversation.isLoading}
              >
                Avançar
              </Button>
            </ButtonGroup>{' '}
          </Stack>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};

export default PopoverNewConversation;
