import {
  Box,
  Button,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useBoolean,
  useToast,
} from '@chakra-ui/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { colors } from '../../../../constants/colors';
import { CompaniesService } from '../../../../services/companies.service';

const schema = yup
  .object({
    name: yup.string().required('Nome é um campo obrigatório'),
    phoneNumber: yup.string().required('Número é um campo obrigatório'),
  })
  .required();

const GeneralSettingsPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [isLoading, setIsLoading] = useBoolean();
  const toast = useToast();

  useEffect(() => {
    CompaniesService.getCompanyDetails().then(({ data: company }) => {
      setValue('name', company.name);
      setValue('phoneNumber', company.phoneNumber);
    });
  }, []);

  async function onSubmit(data: any) {
    setIsLoading.on();
    CompaniesService.updateCompany(data)
      .then((res) => {
        toast({
          title: 'Opção salva com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      })
      .finally(() => {
        setIsLoading.off();
      });
  }

  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Heading size="lg" mb={1}>
        Dados cadastrais
      </Heading>
      <Text color="gray.500" mb={8}>
        Informações básicas sobre sua empresa
      </Text>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={6}>
          <FormControl>
            <FormLabel>Nome da empresa</FormLabel>
            <Input
              placeholder="Nome"
              {...register('name')}
              isInvalid={errors.name?.message}
              disabled
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.name?.message}
            </Text>
          </FormControl>

          <FormControl>
            <FormLabel>Número whatsapp cadastrado</FormLabel>
            <Input
              {...register('phoneNumber')}
              isInvalid={errors.phoneNumber?.message}
              disabled
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.phoneNumber?.message}
            </Text>
          </FormControl>

          <Divider />

          <Flex justify="flex-end">
            <Button
              width="30%"
              isLoading={isLoading}
              color={colors.white}
              bgColor={colors.primary}
              type="submit"
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default GeneralSettingsPage;
