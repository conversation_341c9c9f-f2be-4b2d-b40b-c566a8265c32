import {
  Table<PERSON>ontainer,
  <PERSON>,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Button,
  Flex,
  Box,
  Text,
  Tooltip,
  useDisclosure,
  Heading,
  Divider,
} from '@chakra-ui/react';
import { ConversationSectorWithIncludes } from '../../../../../../types/ConversationSector';
import { colors } from '../../../../../../constants/colors';
import { MoveConversationCategoryFromSectorModal } from '../MoveConversationCategoryFromSectorModal';
import { ConversationCategory } from '../../../../../../types/Prisma';
import { useState, useCallback, memo } from 'react';
import { RenameConversationCategoryModal } from '../RenameConversationCategoryModal';
import { DeleteConversationCategoryModal } from '../DeleteConversationCategoryModal';
import { CreateConversationCategoryModal } from '../CreateConversationCategoryModal';
import ButtonIcon from '../../../../../../components/ButtonIcon';
import { FaEdit, FaTrashAlt } from 'react-icons/fa';
import { FaArrowRightArrowLeft } from 'react-icons/fa6';
import { scrollbarStyles } from '../../../../../../styles/scrollbar.styles';

interface ConversationSectorCategoriesManagementProps {
  conversationSector: ConversationSectorWithIncludes;
}

export const ConversationSectorCategoriesManagement = memo(
  ({ conversationSector }: ConversationSectorCategoriesManagementProps) => {
    const [selectedConversationCategory, setSelectedConversationCategory] =
      useState<ConversationCategory | null>(null);

    const {
      isOpen: isCreateModalOpen,
      onOpen: openCreateModal,
      onClose: closeCreateModal,
    } = useDisclosure();

    const {
      isOpen: isRenameModalOpen,
      onOpen: openRenameModal,
      onClose: closeRenameModal,
    } = useDisclosure();

    const {
      isOpen: isMoveModalOpen,
      onOpen: openMoveModal,
      onClose: closeMoveModal,
    } = useDisclosure();

    const {
      isOpen: isDeleteModalOpen,
      onOpen: openDeleteModal,
      onClose: closeDeleteModal,
    } = useDisclosure();

    const handleClickCreateConversationCategory = useCallback(() => {
      openCreateModal();
    }, [openCreateModal]);

    const handleClickRenameConversationCategory = useCallback(
      (category: ConversationCategory) => {
        setSelectedConversationCategory(category);
        openRenameModal();
      },
      [openRenameModal],
    );

    const handleClickMoveConversationCategory = useCallback(
      (category: ConversationCategory) => {
        setSelectedConversationCategory(category);
        openMoveModal();
      },
      [openMoveModal],
    );

    const handleClickDeleteConversationCategory = useCallback(
      (category: ConversationCategory) => {
        setSelectedConversationCategory(category);
        openDeleteModal();
      },
      [openDeleteModal],
    );

    const handleCloseCreateModal = useCallback(() => {
      closeCreateModal();
    }, [closeCreateModal]);

    const handleCloseRenameModal = useCallback(() => {
      closeRenameModal();
      setSelectedConversationCategory(null);
    }, [closeRenameModal]);

    const handleCloseMoveModal = useCallback(() => {
      closeMoveModal();
      setSelectedConversationCategory(null);
    }, [closeMoveModal]);

    const handleCloseDeleteModal = useCallback(() => {
      closeDeleteModal();
      setSelectedConversationCategory(null);
    }, [closeDeleteModal]);

    return (
      <Box>
        <Flex justifyContent="space-between">
          <Heading size="sm" mt={2}>
            Categorias de Conversa
          </Heading>
          <Button
            variant="primary"
            size="md"
            mb={4}
            onClick={handleClickCreateConversationCategory}
          >
            + Criar Categoria
          </Button>
        </Flex>
        <Divider />
        {!conversationSector?.categories ||
        conversationSector.categories.length === 0 ? (
          <Text>Não há categorias associadas a este setor</Text>
        ) : (
          <TableContainer css={scrollbarStyles({ height: '3px' })}>
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>Categoria</Th>
                  <Th textAlign="center">Quantidade de conversas</Th>
                  <Th>Ações</Th>
                </Tr>
              </Thead>
              <Tbody>
                {conversationSector.categories.map((conversationCategory) => (
                  <Tr key={`table-${conversationCategory.id}`}>
                    <Td>{conversationCategory.name}</Td>
                    <Td textAlign="center">
                      {conversationCategory._count?.conversations || 0}
                    </Td>
                    <Td>
                      <Flex gap={3}>
                        <Tooltip label="Renomear categoria">
                          <ButtonIcon
                            icon={
                              <FaEdit fontSize="20px" color={colors.darkGrey} />
                            }
                            onClick={() =>
                              handleClickRenameConversationCategory(
                                conversationCategory,
                              )
                            }
                          />
                        </Tooltip>
                        <Tooltip label="Mover categoria">
                          <ButtonIcon
                            icon={
                              <FaArrowRightArrowLeft
                                fontSize="20px"
                                color={colors.darkGrey}
                              />
                            }
                            onClick={() =>
                              handleClickMoveConversationCategory(
                                conversationCategory,
                              )
                            }
                          />
                        </Tooltip>
                        <Tooltip label="Apagar categoria">
                          <ButtonIcon
                            icon={
                              <FaTrashAlt
                                fontSize="20px"
                                color={colors.danger}
                              />
                            }
                            onClick={() =>
                              handleClickDeleteConversationCategory(
                                conversationCategory,
                              )
                            }
                          />
                        </Tooltip>
                      </Flex>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        )}

        <CreateConversationCategoryModal
          isOpen={isCreateModalOpen}
          onClose={handleCloseCreateModal}
          conversationSector={conversationSector}
        />

        {selectedConversationCategory && (
          <>
            <RenameConversationCategoryModal
              isOpen={isRenameModalOpen}
              onClose={handleCloseRenameModal}
              conversationSector={conversationSector}
              conversationCategory={selectedConversationCategory}
            />
            <MoveConversationCategoryFromSectorModal
              isOpen={isMoveModalOpen}
              onClose={handleCloseMoveModal}
              conversationSector={conversationSector}
              conversationCategory={selectedConversationCategory}
            />
            <DeleteConversationCategoryModal
              isOpen={isDeleteModalOpen}
              onClose={handleCloseDeleteModal}
              conversationSector={conversationSector}
              conversationCategory={selectedConversationCategory}
              conversationCount={3}
            />
          </>
        )}
      </Box>
    );
  },
);
