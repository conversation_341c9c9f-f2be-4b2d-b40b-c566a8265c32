import {
  useBoolean,
  Box,
  Heading,
  Stack,
  FormControl,
  FormLabel,
  Divider,
  Flex,
  Button,
  Text,
  useToast,
  Textarea,
} from '@chakra-ui/react';
import { colors } from '../../../../constants/colors';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm } from 'react-hook-form';
import { CompaniesService } from '../../../../services/companies.service';
import { useEffect } from 'react';

const schema = yup
  .object({
    firstContactMessage: yup.string(),
  })
  .required();

const FormWelcomeMessage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });
  const [isLoading, setIsLoading] = useBoolean();
  const toast = useToast();

  useEffect(() => {
    CompaniesService.getCompanyDetails().then(({ data: company }) => {
      setValue('firstContactMessage', company.firstContactMessage);
    });
  }, []);

  async function onSubmit(data: any) {
    setIsLoading.on();
    CompaniesService.updateCompany(data)
      .then((res) => {
        toast({
          title: 'Mensagem inicial salva com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      })
      .finally(() => {
        setIsLoading.off();
      });
  }
  return (
    <Box
      bg="white"
      borderRadius="md"
      border="1px solid"
      borderColor="gray.200"
      p={8}
    >
      <Heading size="lg" mb={1}>
        Mensagem de boas vindas
      </Heading>
      <Text color="gray.500" mb={8}>
        Esta mensagem será enviada quando um cliente iniciar uma conversa com
        sua empresa dentro do horário de atendimento
      </Text>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={6}>
          <FormControl>
            <FormLabel>Mensagem inicial</FormLabel>
            <Textarea
              placeholder="Mensagem inicial"
              {...register('firstContactMessage')}
              isInvalid={errors.firstContactMessage?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.firstContactMessage?.message}
            </Text>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              width="30%"
              isLoading={isLoading}
              color={colors.white}
              bgColor={colors.primary}
              type="submit"
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default FormWelcomeMessage;
