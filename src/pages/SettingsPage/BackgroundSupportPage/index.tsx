import { <PERSON>, Tab, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON>anel, <PERSON>b<PERSON><PERSON><PERSON>, Tabs } from '@chakra-ui/react';
import { colors } from '../../../constants/colors';
import FormBusinessHours from './FormBussinesHours';
import FormWelcomeMessage from './FormWelcomeMessage';
import QuickRepliesPage from './QuickReply';
import OptOutPage from './OptOut';
import AttendantsPage from './Atttedants';

const BackgroundSettingsPage = () => {
  return (
    <Box>
      <Tabs variant="unstyled">
        <TabList borderBottom="1px solid" borderColor="gray.200" mb={4}>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Mensagens Automáticas
          </Tab>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Respostas Rápidas
          </Tab>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Opt-out
          </Tab>
          <Tab
            _selected={{
              color: `${colors.primary}`,
              borderBottom: `2px solid ${colors.primary}`,
            }}
            px={4}
            py={2}
            fontWeight="medium"
            color={colors.darkGrey}
          >
            Atendentes
          </Tab>
        </TabList>

        <TabPanels>
          <TabPanel px={0}>
            <FormWelcomeMessage />
            <Box mt={10}>
              <FormBusinessHours />
            </Box>
          </TabPanel>
          <TabPanel px={0}>
            <QuickRepliesPage />
          </TabPanel>

          <TabPanel px={0}>
            <OptOutPage />
          </TabPanel>

          <TabPanel px={0}>
            <AttendantsPage />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default BackgroundSettingsPage;
