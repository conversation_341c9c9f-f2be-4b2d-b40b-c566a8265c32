import {
  <PERSON>,
  Flex,
  <PERSON><PERSON>,
  Divider,
  Button,
  useToast,
  FormLabel,
  Textarea,
  Text,
} from '@chakra-ui/react';
import { useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import LoadingScreen from '../../../../components/LoadingScreen';
import { apiRoutes } from '../../../../constants/api-routes';
import {
  CompanyBusinessHoursDto,
  CompaniesService,
  CompanyBusinessHours,
} from '../../../../services/companies.service';
import BusinessHoursTable from './components/BusinessHoursTable';
import { colors } from '../../../../constants/colors';

const FormBusinessHours = () => {
  const toast = useToast();
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const [businessHoursData, setBusinessHoursData] = useState<
    CompanyBusinessHoursDto['businessHours']
  >([]);
  const [afterHoursMessage, setAfterHoursMessage] =
    useState<CompanyBusinessHoursDto['afterHoursMessage']>(null);

  const {
    data: getCompanyBusinessHoursResponse,
    isLoading,
    refetch,
  } = useQuery(
    apiRoutes.getCompanyBusinessHours(),
    CompaniesService.getCompanyBusinessHours,
    {
      onSuccess: (response) => {
        setBusinessHoursData(response.data.businessHours);
        setAfterHoursMessage(response.data.afterHoursMessage);
      },
    },
  );

  const updateCompanyBusinessHoursMutation = useMutation(
    CompaniesService.updateCompanyBusinessHours,
    {
      onSuccess: () => {
        setHasChanges(false);
        refetch();
      },
    },
  );

  const handleSaveClick = () => {
    if (!hasChanges) return;
    const promise = updateCompanyBusinessHoursMutation.mutateAsync({
      businessHours: businessHoursData,
      afterHoursMessage: afterHoursMessage,
    });

    toast.promise(promise, {
      loading: {
        title: 'Salvando',
        description: 'Salvando horário de atendimento...',
        duration: 9000,
        isClosable: true,
      },
      success: {
        title: 'Sucesso',
        description: 'Horário de atendimento salvo com sucesso.',
        duration: 5000,
        isClosable: true,
      },
      error: {
        title: 'Erro',
        description: 'Ocorreu um erro ao salvar o horário de atendimento.',
        duration: 5000,
        isClosable: true,
      },
    });
  };

  const handleBusinessHoursDataChange = (data: CompanyBusinessHours[]) => {
    setHasChanges(true);
    setBusinessHoursData(data);
  };

  const handleAfterHoursMessageChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    setHasChanges(true);
    setAfterHoursMessage(event.target.value);
  };

  const handleCancelClick = () => {
    if (!hasChanges) return;
    setBusinessHoursData(
      getCompanyBusinessHoursResponse?.data.businessHours || [],
    );
    setAfterHoursMessage(
      getCompanyBusinessHoursResponse?.data.afterHoursMessage || null,
    );
    setHasChanges(false);
  };

  return (
    <LoadingScreen isLoading={isLoading}>
      <Box
        bg="white"
        borderRadius="md"
        border="1px solid"
        borderColor="gray.200"
        p={8}
      >
        <Heading>Horário de Atendimento</Heading>
        <Text color="gray.500" mb={8}>
          Configure os horários de disponibilidade para atendimento
        </Text>
        <Divider orientation="horizontal" mt={2} />
        <Flex direction="column" gap={8}>
          <BusinessHoursTable
            businessHours={businessHoursData || []}
            onChange={handleBusinessHoursDataChange}
          />
          <Box>
            <FormLabel>Resposta automática</FormLabel>
            <Textarea
              placeholder="Digite aqui uma mensagem aqui"
              value={afterHoursMessage || undefined}
              onChange={handleAfterHoursMessageChange}
            />
            <Text color={colors.middleGrey} size="sm" mt={2}>
              Esta mensagem será enviada automaticamente quando um cliente
              entrar em contato fora do horário de atendimento.
            </Text>
          </Box>

          <Flex gap={2} w="100%" justifyContent="space-between">
            <Button
              variant="outline"
              onClick={handleCancelClick}
              disabled={!hasChanges}
              width="30%"
            >
              Cancelar
            </Button>
            <Button
              variant="primary"
              onClick={handleSaveClick}
              disabled={!hasChanges}
              width="30%"
            >
              Salvar
            </Button>
          </Flex>
        </Flex>
      </Box>
    </LoadingScreen>
  );
};

export default FormBusinessHours;
