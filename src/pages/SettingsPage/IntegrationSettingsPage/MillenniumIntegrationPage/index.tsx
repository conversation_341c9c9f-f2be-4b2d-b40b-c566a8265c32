import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { apiRoutes } from '../../../../constants/api-routes';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { colors } from '../../../../constants/colors';
import {
  IntegrationsService,
  SaveMillenniumCredentialsDto,
} from '../../../../services/integrations.service';

const schema = yup.object({
  millenniumApiUser: yup.string().required('Usuário é obrigatório'),
  millenniumApiPassword: yup.string().required('Senha é obrigatória'),
  millenniumApiHost: yup
    .string()
    .required('Host é obrigatório')
    .matches(
      /^(?!https?:\/\/)(?:\d{1,3}\.){3}\d{1,3}(:\d+)?$|^(?!https?:\/\/)([a-zA-Z0-9-_]+\.)+[a-zA-Z]{2,6}(:\d+)?$/,
      'Host deve ser um IP ou domínio válido e não deve incluir http ou https',
    ),
});

const MillenniumIntegrationPage = () => {
  const toast = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { data: millenniumConfig, refetch: refetchMillenniumConfig } = useQuery(
    apiRoutes.getMillenniumConfig(),
    async () => {
      const { data } = await IntegrationsService.getMillenniumConfig();
      return data;
    },
    {
      onSuccess: (data) => {
        setValue('millenniumApiHost', data.millenniumApiHost || '');
      },
    },
  );

  const saveVndaCredentials = useMutation(
    (data: SaveMillenniumCredentialsDto) =>
      IntegrationsService.saveMillenniumCredentials(data),
    {
      onSuccess: () => {
        refetchMillenniumConfig();
        toast({
          title: 'Configurações salvas com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function onSubmit(data: any) {
    await saveVndaCredentials.mutateAsync(data);
  }

  return (
    <Box>
      <Heading size="md">Configurar integração</Heading>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={4} mt={4}>
          <FormControl>
            <FormLabel>Api Host</FormLabel>
            <Input
              placeholder="Ex.: apiloja.dalla.srv.br:6017"
              {...register('millenniumApiHost')}
              isInvalid={errors.millenniumApiHost?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.millenniumApiHost?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>Usuário</FormLabel>
            <Input
              placeholder="Ex.: usuario"
              {...register('millenniumApiUser')}
              isInvalid={errors.millenniumApiUser?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.millenniumApiUser?.message}
            </Text>
          </FormControl>
          <FormControl>
            <FormLabel>Senha</FormLabel>
            <Input
              placeholder="******************"
              type="password"
              {...register('millenniumApiPassword')}
              isInvalid={errors.millenniumApiPassword?.message}
            />
            <Text color={colors.danger} fontSize="xs">
              {errors.millenniumApiPassword?.message}
            </Text>
          </FormControl>

          <Flex justify="flex-end">
            <Button
              type="submit"
              color={colors.white}
              bgColor={colors.primary}
              width="fit-content"
              isLoading={saveVndaCredentials.isLoading}
            >
              Salvar
            </Button>
          </Flex>
        </Stack>
      </form>
    </Box>
  );
};

export default MillenniumIntegrationPage;
