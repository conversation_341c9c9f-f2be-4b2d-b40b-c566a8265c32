import { IconType } from 'react-icons';
import {
  FaUsers,
  FaComments,
  FaCog,
  FaEnvelope,
  FaBoxOpen,
  FaChartBar,
} from 'react-icons/fa';

export const getModuleIcon = (permission: string): IconType => {
  switch (permission) {
    case 'Clientes':
      return FaUsers;
    case 'Chat':
      return FaComments;
    case 'Configurações':
      return FaCog;
    case 'Campanhas':
      return FaEnvelope;
    case 'Produtos':
      return FaBoxOpen;
    case 'Relatórios':
      return FaChartBar;
    default:
      return FaUsers;
  }
};
