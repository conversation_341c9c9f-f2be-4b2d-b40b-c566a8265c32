import { Box } from '@chakra-ui/react';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import SectorCard from '../CardSector';
import { ConversationSectorsService } from '../../../../../services/conversation-sectors.service';
import { useCrudConversationSectorModal } from '../../../../../hooks/useCrudConversationSectorModal';

const SectorsList = () => {
  const { currentSectorId } = useCrudConversationSectorModal();

  const { data: sectorsData } = useQuery(
    [apiRoutes.listConversationSectors(), currentSectorId],
    async () => {
      const { data } =
        await ConversationSectorsService.listConversationSectors();
      return data;
    },
    {
      keepPreviousData: true,
    },
  );

  return (
    <Box mt={6}>
      {sectorsData?.map((sector, index) => (
        <SectorCard key={sector.id} sector={sector} />
      ))}
    </Box>
  );
};

export default SectorsList;
