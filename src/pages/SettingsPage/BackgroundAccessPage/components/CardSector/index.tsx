import { <PERSON><PERSON>, Box, Flex, Text } from '@chakra-ui/react';
import EntityCard from '../../../../../components/CardEntity';
import { ConversationSectorWithIncludes } from '../../../../../types/ConversationSector';
import { useCrudConversationSectorModal } from '../../../../../hooks/useCrudConversationSectorModal';
import { memo } from 'react';

interface SectorCardProps {
  sector: ConversationSectorWithIncludes;
}

const SectorCard = memo(({ sector }: SectorCardProps) => {
  const { openEditConversationSectorModal, openDeleteConversationSectorAlert } =
    useCrudConversationSectorModal();

  if (!sector) return null;

  const initialLetter = sector?.name
    ? sector.name.charAt(0).toUpperCase()
    : '?';

  const attendantsCount = sector.userConversationSectors?.length || 0;

  const categoriesCount = sector.categories?.length || 0;

  const subtitle = `${attendantsCount} atendente${attendantsCount !== 1 ? 's' : ''} • ${categoriesCount} categoria${categoriesCount !== 1 ? 's' : ''}`;

  const categoryTags = sector.categories?.map((category) => ({
    label: category.name,
    bgColor: 'purple.50',
    color: 'purple.700',
  }));

  const usersDetailsContent = (
    <>
      {sector.userConversationSectors?.map((userSector, index) => {
        const user = userSector.user;
        if (!user) return null;

        return (
          <Flex
            key={index}
            align="center"
            justify="space-between"
            py={2}
            borderBottom={
              index < (sector.userConversationSectors?.length || 0) - 1
                ? '1px solid'
                : 'none'
            }
            borderColor="gray.100"
          >
            <Flex align="center">
              <Avatar
                size="sm"
                name={user.name || user.email}
                mr={3}
                bg="blue.100"
                color="blue.500"
              />
              <Box>
                <Text fontWeight="medium">{user.name || 'Sem nome'}</Text>
                <Text fontSize="sm" color="gray.500">
                  {user.email}
                </Text>
              </Box>
            </Flex>
          </Flex>
        );
      })}
    </>
  );

  return (
    <EntityCard
      title={sector.name}
      subtitle={subtitle}
      iconLetter={initialLetter}
      iconBgColor="blue.100"
      iconColor="blue.500"
      tags={categoryTags}
      detailsTitle="Atendentes deste setor"
      detailsContent={usersDetailsContent}
      onEdit={() => openEditConversationSectorModal(sector.id)}
      onDelete={() => openDeleteConversationSectorAlert(sector.id)}
    />
  );
});

export default SectorCard;
