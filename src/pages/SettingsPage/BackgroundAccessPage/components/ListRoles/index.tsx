import { Box } from '@chakra-ui/react';
import RoleCard from '../CardRole';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../../../constants/api-routes';
import { RoleWithIncludes } from '../../../../../types/Role';
import { RolesService } from '../../../../../services/roles.service';

const RolesList = () => {
  const { data: rolesData } = useQuery(
    apiRoutes.listRoles(),
    async (): Promise<RoleWithIncludes[]> => {
      const { data } = await RolesService.listRoles();
      return data;
    },
  );

  return (
    <Box mt={6}>
      {rolesData?.map((role, index) => <RoleCard key={index} role={role} />)}
    </Box>
  );
};

export default RolesList;
