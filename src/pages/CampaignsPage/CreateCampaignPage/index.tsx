import {
  <PERSON>ert,
  AlertIcon,
  Box,
  Button,
  Flex,
  <PERSON>ing,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { useMutation, useQuery } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { apiRoutes } from '../../../constants/api-routes';
import { appPaths } from '../../../constants/app-paths';
import { colors } from '../../../constants/colors';
import { MAX_CAMPAIGN_RECIPIENTS } from '../../../constants/max-campaign-recipients';
import {
  CampaignExperimentsService,
  StartOrScheduleCampaignExperimentDto,
} from '../../../services/campaign-experiments.service';
import { CompaniesService } from '../../../services/companies.service';
import {
  ListMessageTemplateItem,
  MessageTemplatesService,
} from '../../../services/message-templates.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import {
  SendOrScheduleSmsCampaignDto,
  SmsCampaignsService,
} from '../../../services/sms-campaigns.service';
import {
  SendOrScheduleWhatsappCampaignDto,
  WhatsappCampaignsService,
} from '../../../services/whatsapp-campaigns.service';
import {
  finishCampaignCreation,
  isExperiment,
  isValidCampaignCreationState,
} from '../../../state/campaignCreationSlice';
import { RootState } from '../../../state/store';
import { CompanyDefinedFieldTableEnum } from '../../../types/CompanyDefinedField';
import SectionABTesting from './components/SectionABTesting';
import SectionCampaignRecipients from './components/SectionCampaignRecipients';
import SectionCommunicationChannel from './components/SectionCommunicationChannel';
import SectionScheduling from './components/SectionScheduling';
import SectionSelectTemplate from './components/SectionSelectTemplate';
import SectionNumberQualityRatingNotice from './components/SectionNumberQualityRatingNotice';
import AlertDialogBase from '../../../components/AlertDialog';
import { useEffect, useState } from 'react';
import { BiCrown } from 'react-icons/bi';
import CustomTag from '../../../components/CustomTag';
import { CommunicationChannelEnum } from '../../../types/CommunicationChannelEnum';
import SectionSelectEmailTemplate from './components/SectionSelectEmailTemplate';
import { EmailTemplatesService } from '../../../services/email-templates.service';
import {
  EmailCampaignsService,
  SendOrScheduleEmailCampaignDto,
} from '../../../services/email-campaigns.service';

const CreateCampaignPage = () => {
  const {
    selectedCustomerRows,
    scheduledExecutionTime,
    selectedTemplate,
    selectedEmailTemplate,
    templateArgs,
    filterCriteria,
    communicationChannel,
    variants,
    winningMetric,
    testSizePercentage,
    durationInMinutes,
    campaignRecommendation,
  } = useSelector((state: RootState) => state.campaignCreation);
  const toast = useToast();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const selectedCustomerIds = Object.keys(selectedCustomerRows).slice(
    0,
    MAX_CAMPAIGN_RECIPIENTS,
  );
  const canSubmit = useSelector(isValidCampaignCreationState);
  const isCampaignExperiment = useSelector(isExperiment);
  const { data: phoneRating } = useQuery(
    apiRoutes.showPhoneQualityRating(),
    async () => {
      const { data } = await MessageTemplatesService.showPhoneQualityRating();
      return data;
    },
    {
      staleTime: 1000 * 60 * 60,
    },
  );
  const { data: templates = [], refetch: refetchTemplates } = useQuery(
    apiRoutes.listMessageTemplates(),
    async () => {
      const { data } = await MessageTemplatesService.listMessageTemplates();
      return data;
    },
    {
      select(data) {
        return data.filter(
          (template: ListMessageTemplateItem) => template.status === 'approved',
        );
      },
    },
  );

  const { data: emailTemplates = [] } = useQuery(
    apiRoutes.listEmailTemplates(),
    async () => {
      const { data } = await EmailTemplatesService.listEmailTemplates();
      return data;
    },
  );

  const { data: companyDefinedFields = [] } = useQuery(
    apiRoutes.listCompanyDefinedFields(CompanyDefinedFieldTableEnum.CUSTOMERS),
    async () => {
      const { data } = await CompaniesService.listCompanyDefinedFields(
        CompanyDefinedFieldTableEnum.CUSTOMERS,
      );
      return data;
    },
  );
  const [
    isPhoneQualityRatingLowAlertOpen,
    setIsPhoneQualityRatingLowAlertOpen,
  ] = useState(false);

  const sendOrScheduleWhatsappCampaign = useMutation(
    (sendOrScheduleWhatsappCampaignDto: SendOrScheduleWhatsappCampaignDto) =>
      WhatsappCampaignsService.sendOrScheduleWhatsappCampaign(
        sendOrScheduleWhatsappCampaignDto,
      ),
    {
      onSuccess: async () => {
        MixpanelService.track('send-multiple-messages');
        dispatch(finishCampaignCreation());
        toast({
          title: 'Campanha criada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate(appPaths.campaigns.whatsapp.index());
      },
    },
  );

  const sendOrScheduleSmsCampaign = useMutation(
    (sendOrScheduleSmsCampaignDto: SendOrScheduleSmsCampaignDto) =>
      SmsCampaignsService.sendOrScheduleSmsCampaign(
        sendOrScheduleSmsCampaignDto,
      ),
    {
      onSuccess: async () => {
        MixpanelService.track('send-multiple-messages');
        dispatch(finishCampaignCreation());
        toast({
          title: 'Campanha criada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate(appPaths.campaigns.sms.index());
      },
    },
  );

  const sendOrScheduledEmailCampaign = useMutation(
    (sendOrScheduleEmailCampaignDto: SendOrScheduleEmailCampaignDto) =>
      EmailCampaignsService.sendOrScheduleEmailCampaign(
        sendOrScheduleEmailCampaignDto,
      ),
    {
      onSuccess: async () => {
        MixpanelService.track('send-multiple-emails');
        dispatch(finishCampaignCreation());
        toast({
          title: 'Campanha de Email criada com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate(appPaths.campaigns.email.index());
      },
    },
  );

  const startOrScheduleCampaignExperimentMutation = useMutation(
    (
      startOrScheduleCampaignExperimentDto: StartOrScheduleCampaignExperimentDto,
    ) => {
      return CampaignExperimentsService.startOrScheduleCampaignExperiment(
        startOrScheduleCampaignExperimentDto,
      );
    },
    {
      onSuccess: async () => {
        dispatch(finishCampaignCreation());
        toast({
          title: 'Experimento criado com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        navigate(appPaths.campaigns.whatsapp.index());
      },
    },
  );

  useEffect(() => {
    if (campaignRecommendation?.title) {
      toast({
        title: `Recomendação aplicada: ${campaignRecommendation.title}`,
        description: 'Campos preenchidos automaticamente.',
        status: 'info',
        duration: 5000,
        isClosable: true,
        position: 'top-right',
      });
    }
  }, [campaignRecommendation?.title]);

  async function handleClickSubmit() {
    const templateId = selectedTemplate?.id || selectedEmailTemplate?.id;

    if (!templateId || !selectedCustomerIds.length) return;

    if (communicationChannel === 'sms') {
      return await sendOrScheduleSmsCampaign.mutateAsync({
        customerIds: selectedCustomerIds,
        templateId,
        templateArgs,
        filterCriteria: filterCriteria || undefined,
        scheduledExecutionTime: scheduledExecutionTime || null,
      });
    }

    if (communicationChannel === CommunicationChannelEnum.EMAIL) {
      return await sendOrScheduledEmailCampaign.mutateAsync({
        customerIds: selectedCustomerIds,
        emailTemplateId: templateId,
        templateArgs,
        filterCriteria: filterCriteria || undefined,
        scheduledExecutionTime: scheduledExecutionTime || null,
      });
    }

    if (isCampaignExperiment) {
      return await startOrScheduleCampaignExperimentMutation.mutateAsync({
        customerIds: selectedCustomerIds,
        filterCriteria: filterCriteria || undefined,
        scheduledExecutionTime: scheduledExecutionTime || null,
        name: 'Experimento',
        variants: [
          {
            templateId,
            templateArgs,
          },
          ...variants,
        ],
        winningMetric,
        testSizePercentage,
        durationInMinutes,
      });
    }
    if (phoneRating?.rating === 'low') {
      setIsPhoneQualityRatingLowAlertOpen(true);
      return;
    }

    handleSendOrScheduleWhatsappCampaign();
  }

  async function handleSendOrScheduleWhatsappCampaign() {
    const templateId = selectedTemplate?.id!;
    return await sendOrScheduleWhatsappCampaign.mutateAsync({
      customerIds: selectedCustomerIds,
      templateId,
      templateArgs,
      filterCriteria: filterCriteria || undefined,
      scheduledExecutionTime: scheduledExecutionTime || null,
    });
  }

  function handleClickCancel() {
    dispatch(finishCampaignCreation());
    navigate(appPaths.customers.index());
  }

  return (
    <Box paddingTop="50px" paddingBottom="50px" paddingX="100px">
      <AlertDialogBase
        isOpen={isPhoneQualityRatingLowAlertOpen}
        onClose={() => setIsPhoneQualityRatingLowAlertOpen(false)}
        title="Tem certeza de que gostaria de enviar esta campanha?"
        onConfirm={() => {
          setIsPhoneQualityRatingLowAlertOpen(false);
          handleSendOrScheduleWhatsappCampaign();
        }}
      >
        A qualidade do seu número está baixa. Recomendamos que espere alguns
        dias até que a qualidade do seu número aumente para garantir que sua
        conta não seja bloqueada pela Meta (antigo Facebook) por 30 dias.
      </AlertDialogBase>

      <Stack spacing={5}>
        {phoneRating?.rating === 'low' ? (
          <SectionNumberQualityRatingNotice />
        ) : (
          <></>
        )}
        <Flex justifyContent={'space-between'}>
          <Heading size="md">Nova campanha</Heading>
          {campaignRecommendation && (
            <Flex alignItems={'center'} gap={3}>
              <Text color={colors.darkGrey}>Usando recomendação:</Text>
              <CustomTag
                bgColor="blue.50"
                textColor="blue.600"
                label={campaignRecommendation?.title}
                icon={<BiCrown />}
              />
            </Flex>
          )}
        </Flex>
        <SectionCommunicationChannel />
        {communicationChannel === CommunicationChannelEnum.EMAIL ? (
          <SectionSelectEmailTemplate
            emailTemplates={emailTemplates}
            refetchTemplates={refetchTemplates}
            companyDefinedFields={companyDefinedFields}
          />
        ) : (
          <SectionSelectTemplate
            templates={templates}
            refetchTemplates={refetchTemplates}
            companyDefinedFields={companyDefinedFields}
          />
        )}
        {communicationChannel === 'whatsapp' && (
          <SectionABTesting
            templates={templates}
            companyDefinedFields={companyDefinedFields}
          />
        )}
        <SectionCampaignRecipients />
        <SectionScheduling />
        <Box>
          {selectedTemplate?.status === 'pending' && (
            <Alert status="warning">
              <AlertIcon />O template selecionado está com status "pendente".
              Caso ele não seja aprovado até o horário de envio, a campanha será
              cancelada.
            </Alert>
          )}
        </Box>
        <Flex justifyContent={'space-between'}>
          <Button onClick={handleClickCancel}>Cancelar</Button>
          <Button
            color={colors.white}
            bgColor={colors.primary}
            onClick={handleClickSubmit}
            isDisabled={!canSubmit}
            isLoading={
              sendOrScheduleWhatsappCampaign.isLoading ||
              sendOrScheduleSmsCampaign.isLoading ||
              startOrScheduleCampaignExperimentMutation.isLoading ||
              sendOrScheduledEmailCampaign.isLoading
            }
          >
            Confirmar
          </Button>
        </Flex>
      </Stack>
    </Box>
  );
};

export default CreateCampaignPage;
