import { <PERSON>, Divider, Flex, Heading } from '@chakra-ui/react';
import { BiConversation, BiSolidMessageX } from 'react-icons/bi';
import { MdCampaign, MdMarkEmailRead, MdOutlineEmail } from 'react-icons/md';
import { PiArrowsClockwiseBold } from 'react-icons/pi';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import Header from '../components/Header';
import TableEmailCampaigns from './components/TableEmailCampaigns';
import { BsFillEyeFill } from 'react-icons/bs';
import { useQuery } from 'react-query';
import { EmailCampaignsService } from '../../../services/email-campaigns.service';
import { NumberUtils } from '../../../utils/number.utils';
import CardStatistic from '../../../components/CardStatistic';

const EmailCampaignsPage = () => {
  const { data: campaignsSummary } = useQuery(
    apiRoutes.getEmailCampaignsSummary(),
    async () => {
      return await EmailCampaignsService.getEmailCampaignsSummary();
    },
  );

  return (
    <Box padding="20px">
      <Header title="Campanhas de Email" icon={<MdOutlineEmail />} />
      <Flex justifyContent="space-evenly" gap={3} alignItems="center">
        {[
          {
            icon: <MdCampaign />,
            title: 'Total de campanhas',
            value: campaignsSummary?.totalEmailCampaigns || '',
            bgIconColor: colors.greenMedium,
            tooltip: 'Quantidade de Campanhas feitas',
          },
          {
            icon: <BiConversation />,
            title: 'Total de envios',
            value: campaignsSummary?.totalEmailsSent || '',
            bgIconColor: colors.secondary,
            tooltip: 'Quantidade de envios de email',
          },
          {
            icon: <MdMarkEmailRead />,
            title: 'Taxa de Entrega',
            value: NumberUtils.getPercentValue(
              campaignsSummary?.totalEmailsDelivered,
              campaignsSummary?.totalEmailsSent,
            ),
            bgIconColor: colors.primaryLight,
            tooltip: `${campaignsSummary?.totalEmailsDelivered} entregues de ${campaignsSummary?.totalEmailsSent} enviados`,
          },
          {
            icon: <BsFillEyeFill />,
            title: 'Taxa de Abertura',
            value: NumberUtils.getPercentValue(
              campaignsSummary?.totalEmailsOpened,
              campaignsSummary?.totalEmailsDelivered,
            ),
            bgIconColor: colors.primaryLight,
            tooltip: `${campaignsSummary?.totalEmailsOpened} abertos de ${campaignsSummary?.totalEmailsDelivered} entregues`,
          },
          {
            icon: <PiArrowsClockwiseBold />,
            title: 'Taxa de Cliques',
            value: NumberUtils.getPercentValue(
              campaignsSummary?.totalEmailsEngaged,
              campaignsSummary?.totalEmailsOpened,
            ),
            bgIconColor: colors.green,
            tooltip: `${campaignsSummary?.totalEmailsEngaged} emails com cliques de ${campaignsSummary?.totalEmailsOpened} abertos`,
          },
          {
            icon: <BiSolidMessageX />,
            title: 'Bounce Rate',
            value: NumberUtils.getPercentValue(
              campaignsSummary?.totalEmailsBounced,
              campaignsSummary?.totalEmailsSent,
            ),
            bgIconColor: colors.red,
            tooltip: `${campaignsSummary?.totalEmailsBounced} bounces de ${campaignsSummary?.totalEmailsSent} enviados`,
          },
        ].map(({ icon, title, value, bgIconColor, tooltip }) => (
          <CardStatistic
            value={value}
            key={title}
            icon={icon}
            title={title}
            bgIconColor={bgIconColor}
            tooltip={tooltip}
            minHeigth={'132px'}
          />
        ))}
      </Flex>
      <Flex justifyContent={'space-between'} mt={10} alignItems="center">
        <Heading size="md">Campanhas recentes</Heading>
      </Flex>
      <Divider orientation="horizontal" mt={2} />
      {<TableEmailCampaigns />}
    </Box>
  );
};

export default EmailCampaignsPage;
