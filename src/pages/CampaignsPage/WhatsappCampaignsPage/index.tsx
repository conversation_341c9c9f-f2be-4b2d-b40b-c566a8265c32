import {
  Box,
  Button,
  Divider,
  Flex,
  Heading,
  Input,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
  Radio,
  RadioGroup,
  Stack,
  Tab,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  useBoolean,
  useToast,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import {
  addDays,
  startOfDay,
  startOfMonth,
  startOfWeek,
  subDays,
} from 'date-fns';
import { useState } from 'react';
import { BiConversation, BiSolidMessageX, BiPhone } from 'react-icons/bi';
import { FaPray, FaWhatsapp } from 'react-icons/fa';
import { MdCampaign } from 'react-icons/md';
import { PiArrowsClockwiseBold } from 'react-icons/pi';
import { TbTableExport } from 'react-icons/tb';
import { apiRoutes } from '../../../constants/api-routes';
import { colors } from '../../../constants/colors';
import useDownloadFile from '../../../hooks/useDownloadFile';
import { ReportsService } from '../../../services/reports.service';
import CardStatisticContainer from '../../../components/CardStatisticContainer';
import Header from '../components/Header';
import TableWhatsappCampaigns from './components/TableWhatsappCampaigns';
import ConfirmCancelButtons from '../../../components/ConfirmCancelButtons';

const ButtonExportCampaignResults = ({
  selectedCampaignsToExport,
}: {
  selectedCampaignsToExport: Record<string, boolean>;
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const { downloadExcelReport } = useDownloadFile();

  const hasSelectedCampaigns =
    Object.keys(selectedCampaignsToExport).length > 0;

  const [isExporting, setIsExporting] = useBoolean(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [selectedPeriodOption, setSelectedPeriodOption] = useState('');

  const predefinedOptions = [
    {
      label: 'Últimos 7 dias',
      value: {
        startDate: startOfDay(subDays(new Date(), 7)).toISOString(),
        endDate: new Date().toISOString(),
      },
    },
    {
      label: 'Últimos 30 dias',
      value: {
        startDate: startOfDay(subDays(new Date(), 30)).toISOString(),
        endDate: new Date().toISOString(),
      },
    },
    {
      label: 'Semana atual',
      value: {
        startDate: startOfWeek(new Date()).toISOString(),
        endDate: new Date().toISOString(),
      },
    },
    {
      label: 'Semana anterior',
      value: {
        startDate: startOfWeek(subDays(new Date(), 7)).toISOString(),
        endDate: new Date().toISOString(),
      },
    },
    {
      label: 'Mês atual',
      value: {
        startDate: startOfMonth(new Date()).toISOString(),
        endDate: new Date().toISOString(),
      },
    },
  ];

  const selectedCampaignsCount = Object.keys(selectedCampaignsToExport).length;

  async function handleClickExportCampaigns({
    exportSelected,
  }: {
    exportSelected: boolean;
  }) {
    setIsExporting.on();
    try {
      if (exportSelected && selectedCampaignsCount > 30) {
        toast({
          title: 'Limite de exportação excedido',
          description: 'Você pode exportar no máximo 30 campanhas por vez.',
          status: 'warning',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      let dataToExport;
      if (exportSelected) {
        const { data } = await ReportsService.exportCampaignSalesReport({
          campaignsIds: exportSelected
            ? Object.keys(selectedCampaignsToExport).join(',')
            : undefined,
        });
        dataToExport = data;
      } else {
        const { data } = await ReportsService.exportCampaignSalesReport({
          startDate: new Date(startDate).toISOString(),
          endDate: addDays(new Date(endDate), 1).toISOString(),
        });
        dataToExport = data;
      }

      downloadExcelReport(dataToExport, 'resultado-de-campanhas.xlsx');
      toast({
        title: 'Exportação realizada com sucesso',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      onClose();
    } finally {
      setIsExporting.off();
    }
  }

  function handleClose() {
    setStartDate('');
    setEndDate('');
    setSelectedPeriodOption('');
    onClose();
  }

  return (
    <>
      <Button
        bgColor={colors.primary}
        color={colors.white}
        leftIcon={<TbTableExport size={20} />}
        onClick={onOpen}
        borderRadius="md"
        px={5}
        py={2}
      >
        Exportar
        {hasSelectedCampaigns && ` (${selectedCampaignsCount})`}
      </Button>

      <Modal isOpen={isOpen} onClose={handleClose} isCentered size="md">
        <ModalOverlay />
        <ModalContent borderRadius="lg">
          <ModalHeader fontWeight="bold" fontSize="lg">
            Exportar dados de campanhas
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Tabs isFitted variant="unstyled">
              <TabList mb={4} gap={2}>
                {hasSelectedCampaigns && (
                  <Tab
                    fontWeight="semibold"
                    _selected={{
                      borderBottom: '2px solid',
                      borderColor: 'blue.500',
                      color: 'blue.600',
                    }}
                  >
                    Exportar selecionadas
                  </Tab>
                )}
                <Tab
                  fontWeight="semibold"
                  _selected={{
                    borderBottom: '2px solid',
                    borderColor: 'blue.500',
                    color: 'blue.600',
                  }}
                >
                  Exportar por data
                </Tab>
              </TabList>

              <TabPanels>
                {hasSelectedCampaigns && (
                  <TabPanel>
                    <Stack
                      spacing={3}
                      borderWidth="1px"
                      borderRadius="md"
                      p={4}
                      bg="gray.50"
                    >
                      <Text fontWeight="medium" fontSize="sm" color="green.600">
                        ✅ {selectedCampaignsToExport.length} campanha(s)
                        selecionada(s)
                      </Text>
                      <Text fontSize="sm" color="gray.600">
                        Os dados serão exportados em formato XLSX.
                      </Text>
                    </Stack>

                    <ConfirmCancelButtons
                      onConfirmClick={() => {
                        handleClickExportCampaigns({ exportSelected: true });
                      }}
                      onCancelClick={handleClose}
                      isLoading={isExporting}
                    />
                  </TabPanel>
                )}

                <TabPanel>
                  <Stack spacing={5}>
                    <Stack direction="row" spacing={4}>
                      <Stack flex={1}>
                        <Text fontWeight="medium" fontSize="sm">
                          Data inicial
                        </Text>
                        <Input
                          type="date"
                          value={startDate}
                          onChange={(e) => {
                            setStartDate(e.target.value);
                            setSelectedPeriodOption('');
                          }}
                          bg="white"
                          borderColor="gray.300"
                          _focus={{
                            borderColor: 'blue.500',
                            boxShadow: '0 0 0 1px #3182ce',
                          }}
                        />
                      </Stack>
                      <Stack flex={1}>
                        <Text fontWeight="medium" fontSize="sm">
                          Data final
                        </Text>
                        <Input
                          type="date"
                          value={endDate}
                          onChange={(e) => {
                            setEndDate(e.target.value);
                            setSelectedPeriodOption('');
                          }}
                          bg="white"
                          borderColor="gray.300"
                          _focus={{
                            borderColor: 'blue.500',
                            boxShadow: '0 0 0 1px #3182ce',
                          }}
                        />
                      </Stack>
                    </Stack>

                    <RadioGroup
                      onChange={(value) => {
                        setSelectedPeriodOption(value);
                        const predefinedOption = predefinedOptions.find(
                          (option) => option.label === value,
                        );
                        setStartDate(predefinedOption?.value.startDate!);
                        setEndDate(predefinedOption?.value.endDate!);
                      }}
                      value={selectedPeriodOption}
                    >
                      <Stack spacing={2}>
                        {predefinedOptions.map(({ label }) => (
                          <Radio
                            key={label}
                            value={label}
                            colorScheme="blue"
                            _checked={{
                              bg: 'blue.100',
                              borderColor: 'blue.500',
                            }}
                          >
                            <Text fontSize="sm">{label}</Text>
                          </Radio>
                        ))}
                      </Stack>
                    </RadioGroup>

                    <ConfirmCancelButtons
                      onConfirmClick={() =>
                        handleClickExportCampaigns({ exportSelected: false })
                      }
                      onCancelClick={handleClose}
                      isLoading={isExporting}
                    />
                  </Stack>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

const WhatsappCampaignsPage = () => {
  const [selectedCampaignsToExport, setSelectedCampaignsToExport] = useState<
    Record<string, boolean>
  >({});

  const cardsData = [
    {
      icon: <BiPhone />,
      title: 'Qualidade do Nº',
      requestRoute: apiRoutes.showPhoneQualityRating(),
      valueFormatter: (value: { rating: string }) => {
        switch (value?.rating) {
          case 'high':
            return 'Alta';
          case 'medium':
            return 'Média';
          case 'low':
            return 'Baixa';
          default:
            return 'N/A';
        }
      },
      bgIconColor: getColorByPhoneQualityRating,
      tooltip:
        'Refere-se a qualidade do seu número na Meta (antigo Facebook); os valores possíveis são "Baixo", "Médio" e "Alto"; quando "Baixo" é preciso ter cuidado com o que é enviado em campanhas pois a Meta pode bloquear seu número por 30 dias',
      valueColor: getColorByPhoneQualityRating,
    },
    {
      icon: <MdCampaign />,
      title: 'Total de campanhas',
      requestRoute: apiRoutes.getTotalWhatsappCampaigns(new Date(), new Date()),
      bgIconColor: colors.primaryLight,
    },
    {
      icon: <BiConversation />,
      title: 'Total de disparos',
      requestRoute: apiRoutes.getTotalMesssagesSent(new Date(), new Date()),
      bgIconColor: colors.secondary,
      tooltip: 'Total de mensagens enviadas em campanhas',
    },
    {
      icon: <PiArrowsClockwiseBold />,
      title: 'Engajamento',
      requestRoute: apiRoutes.getMessagesEngagementRate(new Date(), new Date()),
      valueFormatter: (value: any) => `${(value || 0).toFixed(2)}%`,
      bgIconColor: colors.green,
      tooltip:
        'Percentual de mensagens que tiveram respostas ou cliques nos link',
    },
    {
      icon: <BiSolidMessageX />,
      title: 'Bounce Rate',
      requestRoute: apiRoutes.getMessagesBounceRate(new Date(), new Date()),
      valueFormatter: (value: any) => `${(value || 0).toFixed(2)}%`,
      bgIconColor: colors.red,
      tooltip: 'Percentual de mensagens que não foram entregues',
    },
  ];

  function getColorByPhoneQualityRating(value: { rating: string }) {
    switch (value?.rating) {
      case 'high':
        return colors.greenMedium;
      case 'medium':
        return colors.yellowMedium;
      case 'low':
        return colors.danger;
      default:
        return colors.lightGrey;
    }
  }

  function toggleSelectCampaign(campaignId: string) {
    setSelectedCampaignsToExport((prev) => ({
      ...prev,
      [campaignId]: !prev[campaignId],
    }));
  }

  function toggleSelectAllCampaigns(campaignIds: string[]) {
    setSelectedCampaignsToExport((prev) => {
      const allSelected = campaignIds.every((id) => prev[id]);

      if (allSelected) {
        return {};
      } else {
        const newState: Record<string, boolean> = { ...prev };
        campaignIds.forEach((id) => {
          newState[id] = true;
        });
        return newState;
      }
    });
  }

  const deselectAllCampaigns = () => {
    setSelectedCampaignsToExport({});
  };

  return (
    <Box padding="20px">
      <Header title="Campanhas de WhatsApp" icon={<FaWhatsapp />} />
      <Flex justifyContent="space-between" gap={3} alignItems="center">
        {cardsData.map(
          ({
            icon,
            requestRoute,
            title,
            bgIconColor,
            valueFormatter,
            tooltip,
            valueColor,
          }) => (
            <CardStatisticContainer
              key={title}
              icon={icon}
              title={title}
              requestRoute={requestRoute}
              valueFormatter={valueFormatter}
              bgIconColor={bgIconColor}
              tooltip={tooltip}
              valueColor={valueColor}
            />
          ),
        )}
      </Flex>
      <Flex justifyContent={'space-between'} mt={10} alignItems="center">
        <Heading size="md">Campanhas recentes</Heading>
        <ButtonExportCampaignResults
          selectedCampaignsToExport={selectedCampaignsToExport}
        />
      </Flex>
      <TableWhatsappCampaigns
        toggleSelectCampaign={toggleSelectCampaign}
        selectedWhatsappCampaigns={selectedCampaignsToExport}
        toggleSelectAllCampaigns={toggleSelectAllCampaigns}
        deselectAllCampaigns={deselectAllCampaigns}
      />
    </Box>
  );
};

export default WhatsappCampaignsPage;
