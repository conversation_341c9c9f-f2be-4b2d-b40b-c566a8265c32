import {
  Box,
  Card,
  CardBody,
  Heading,
  Text,
  Button,
  Badge,
} from '@chakra-ui/react';
import { FiArrowRight, FiCheckCircle, FiXCircle } from 'react-icons/fi';
import { CashbackPlatform } from '../../types/CashbackPlataform';
import { colors } from '../../../../../constants/colors';

interface PlatformCardProps {
  platform: CashbackPlatform;
  onSelect: (platform: CashbackPlatform) => void;
}

const PlatformCard = ({ platform, onSelect }: PlatformCardProps) => {
  return (
    <Card
      bg="white"
      borderWidth="1px"
      borderColor={colors.lightGrey}
      borderRadius="xl"
      overflow="hidden"
      cursor="pointer"
      onClick={() => onSelect(platform)}
      position="relative"
      transition="all 0.3s"
      _hover={{
        transform: 'translateY(-4px)',
        shadow: 'lg',
      }}
    >
      <Box position="absolute" top="4" right="4">
        {platform.isSupported ? (
          <Badge
            display="flex"
            alignItems="center"
            gap={1.5}
            px={3}
            py={1}
            borderRadius="full"
            colorScheme="green"
            variant="subtle"
          >
            <FiCheckCircle />
            <Text>Suportado</Text>
          </Badge>
        ) : (
          <Badge
            display="flex"
            alignItems="center"
            gap={1.5}
            px={3}
            py={1}
            borderRadius="full"
            colorScheme="red"
            variant="subtle"
          >
            <FiXCircle />
            <Text>Não suportado</Text>
          </Badge>
        )}
      </Box>

      <CardBody p={6}>
        <Heading size="md" mb={4}>
          {platform.name}
        </Heading>

        {platform.description && (
          <Text color={colors.darkGrey} mb={6}>
            {platform.description}
          </Text>
        )}

        <Button
          w="full"
          variant={platform.isSupported ? 'primary' : 'outline'}
          isDisabled={!platform.isSupported}
          rightIcon={platform.isSupported ? <FiArrowRight /> : undefined}
        >
          {platform.isSupported ? 'Configurar Cashback' : 'Em breve'}
        </Button>
      </CardBody>

      <Box
        position="absolute"
        bottom="0"
        left="0"
        w="full"
        h="1"
        bg={colors.primary}
        transition="background-color 0.3s"
      />
    </Card>
  );
};

export default PlatformCard;
