import { SimpleGrid } from '@chakra-ui/react';
import PlatformCard from '../PlatformCard';
import { CashbackPlatform } from '../../types/CashbackPlataform';

interface PlatformGrid {
  platforms: CashbackPlatform[];
  onSelect: (platform: CashbackPlatform) => void;
}

const PlatformGrid = ({ platforms, onSelect }: PlatformGrid) => {
  return (
    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6} mt={4}>
      {platforms.map((platform) => (
        <PlatformCard
          key={platform.id}
          platform={platform}
          onSelect={onSelect}
        />
      ))}
    </SimpleGrid>
  );
};

export default PlatformGrid;
