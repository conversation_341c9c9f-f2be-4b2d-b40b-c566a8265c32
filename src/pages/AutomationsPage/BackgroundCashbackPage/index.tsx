import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON>b,
  <PERSON>b<PERSON>ist,
  TabPanel,
  TabPanels,
  Tabs,
  useToast,
} from '@chakra-ui/react';
import CashbackConfig from './components/CashbackConfig';
import { useState, useEffect } from 'react';
import { platforms } from './constants/platforms';
import PlatformGrid from './components/PlatformGrid';
import { CashbackPlatform } from './types/CashbackPlataform';
import { useQuery } from 'react-query';
import { apiRoutes } from '../../../constants/api-routes';
import { IntegrationsService } from '../../../services/integrations.service';
import IntegrationActiveKeyBySourceIntegration from '../BackgroundAutomationsPage/constants/integration-active-key-by-source-integration';

const CashbackPage = () => {
  const [tabIndex, setTabIndex] = useState(0);
  const [selectedPlatform, setSelectedPlatform] =
    useState<CashbackPlatform | null>(null);
  const toast = useToast();

  const { data: integrationStatus } = useQuery(
    apiRoutes.getIntegrationStatusSummary(),
    async () => {
      const { data } = await IntegrationsService.getIntegrationStatusSummary();
      return data;
    },
  );

  const handleSelectPlatform = (platform: CashbackPlatform) => {
    if (!platform.isSupported) return;

    if (integrationStatus) {
      const integrationKey =
        IntegrationActiveKeyBySourceIntegration[platform.id];
      const isActive = integrationKey
        ? integrationStatus[integrationKey as keyof typeof integrationStatus]
        : false;

      if (!isActive) {
        toast({
          title: 'Integração inativa',
          description: `A integração com ${platform.name} está inativa. Ative-a nas configurações antes de prosseguir.`,
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      setSelectedPlatform(platform);
      setTabIndex(1);
    }
  };

  return (
    <Box padding="20px">
      <Flex width="100%" justifyContent="space-between" alignItems="center">
        <Heading mb={5}>Cashback</Heading>
      </Flex>
      <Divider orientation="horizontal" mt={2} />
      <Tabs index={tabIndex} onChange={setTabIndex}>
        <TabList>
          <Tab>Plataformas</Tab>
          <Tab isDisabled={!selectedPlatform}>Configuração</Tab>
        </TabList>
        <TabPanels>
          <TabPanel>
            <PlatformGrid
              platforms={platforms}
              onSelect={handleSelectPlatform}
            />
          </TabPanel>
          <TabPanel>
            {selectedPlatform && (
              <CashbackConfig
                integration={selectedPlatform}
                setTabIndex={setTabIndex}
              />
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default CashbackPage;
