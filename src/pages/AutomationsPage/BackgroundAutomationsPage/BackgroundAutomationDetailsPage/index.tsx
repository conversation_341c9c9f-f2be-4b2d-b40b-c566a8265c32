import {
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CloseIcon,
  EmailIcon,
  QuestionIcon,
  RepeatIcon,
  SearchIcon,
  TimeIcon,
  WarningIcon,
} from '@chakra-ui/icons';
import {
  Badge,
  Box,
  Button,
  Checkbox,
  CheckboxGroup,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Popover,
  PopoverArrow,
  PopoverCloseButton,
  PopoverContent,
  PopoverTrigger,
  Select,
  Slide,
  Stack,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  useDisclosure,
  useOutsideClick,
  VStack,
} from '@chakra-ui/react';
import { addDays, endOfMonth, format, startOfMonth } from 'date-fns';
import { useState } from 'react';
import ReactDatePicker from 'react-datepicker';
import { AiOutlineAreaChart } from 'react-icons/ai';
import { BsFillEyeFill, BsPeopleFill } from 'react-icons/bs';
import { FaBox, FaFunnelDollar } from 'react-icons/fa';
import { GiPayMoney } from 'react-icons/gi';
import { HiCursorClick } from 'react-icons/hi';
import { MdAttachMoney } from 'react-icons/md';
import { TbTableExport } from 'react-icons/tb';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';
import BreadcrumbBase from '../../../../components/BreadcrumbBase';
import CardStatistic from '../../../../components/CardStatistic';
import CustomECharts from '../../../../components/CustomECharts';
import { apiRoutes } from '../../../../constants/api-routes';
import { appPaths } from '../../../../constants/app-paths';
import { colors } from '../../../../constants/colors';
import useDownloadFile from '../../../../hooks/useDownloadFile';
import { AutomationsService } from '../../../../services/automations.service';
import {
  AutomationSalesData,
  StatisticsService,
} from '../../../../services/statistics.service';
import { ChartUtils, FunnelMetric } from '../../../../utils/chart.utils';
import { MoneyUtils } from '../../../../utils/money.utils';
import { NumberUtils } from '../../../../utils/number.utils';
import { AutomationTypeSlug } from '../../../../types/AutomationTypes';
import {
  AbandonedCartsService,
  AbandonedCartStatus,
} from '../../../../services/abandoned-carts.service';
import Pagination from '../../../../components/Pagination';
import LoadingScreen from '../../../../components/LoadingScreen';
import { FiFilter } from 'react-icons/fi';
import pt from 'date-fns/locale/pt';
import React from 'react';
import './react-datepicker.css';

export const AbandonedCartsTable = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(20);
  const [preStartDate, setPreStartDate] = useState<Date | null>(null);
  const [preEndDate, setPreEndDate] = useState<Date | null>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [preSearchTerm, setPreSearchTerm] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [preSelectedStatuses, setPreSelectedStatuses] = useState<
    (string | number)[]
  >([]);
  const [selectedStatuses, setSelectedStatuses] = useState<(string | number)[]>(
    [],
  );

  const { isOpen, onOpen, onClose } = useDisclosure();

  const { data: abandonedCartsData, isLoading } = useQuery(
    apiRoutes.listAbandonedCarts({
      page: currentPage,
      perPage: rowsPerPage,
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? addDays(new Date(endDate), 1) : undefined,
      status: selectedStatuses.join(','),
      searchQuery: searchTerm,
    }),
    async () => {
      const params = {
        ...(rowsPerPage ? { perPage: rowsPerPage } : { perPage: 20 }),
        ...(startDate && endDate
          ? {
              startDate: new Date(startDate),
              endDate: addDays(new Date(endDate), 1),
            }
          : {}),
        ...(selectedStatuses ? { status: selectedStatuses.join(',') } : {}),
        ...(searchTerm ? { searchQuery: searchTerm } : {}),
      };
      const response = await AbandonedCartsService.listAbandonedCarts({
        page: currentPage,
        ...params,
      });
      return response;
    },
    {
      keepPreviousData: true,
    },
  );

  const translatedStatus = {
    [AbandonedCartStatus.ABANDONED]: 'Abandonado',
    [AbandonedCartStatus.FAILED]: 'Falhou',
    [AbandonedCartStatus.FLOW_TRIGGERED]: 'Fluxo acionado',
    [AbandonedCartStatus.WHATSAPP_SENT]: 'Whatsapp enviado',
  };

  const statusColorMap: Record<
    string,
    { colorScheme: string; icon: JSX.Element }
  > = {
    abandoned: { colorScheme: 'yellow', icon: <TimeIcon boxSize={4} mr={2} /> },
    whatsapp_sent: {
      colorScheme: 'green',
      icon: <EmailIcon boxSize={4} mr={2} />,
    },
    flow_triggered: {
      colorScheme: 'blue',
      icon: <RepeatIcon boxSize={4} mr={2} />,
    },
    failed: { colorScheme: 'red', icon: <WarningIcon boxSize={4} mr={2} /> },
  };

  function handleClearFilters() {
    setPreSearchTerm('');
    setSearchTerm('');
    setPreStartDate(null);
    setPreEndDate(null);
    setStartDate(null);
    setEndDate(null);
    setPreSelectedStatuses([]);
    setSelectedStatuses([]);
    setCurrentPage(1);
    onClose();
  }

  const handleOpenFilters = () => {
    setPreSearchTerm(searchTerm);
    setPreSelectedStatuses(selectedStatuses);
    setPreStartDate(startDate);
    setPreEndDate(endDate);
    onOpen();
  };

  const panelRef = React.useRef<HTMLDivElement>(null);
  useOutsideClick({ ref: panelRef, handler: onClose });

  return (
    <LoadingScreen isLoading={isLoading}>
      <Box gap={3} mt={5}>
        <Flex justifyContent="space-between" mt={14} alignItems="flex-start">
          <Heading mt="50px">Carrinhos abandonados</Heading>
          <Flex direction="column" alignItems="flex-end" gap={3} mt="50px">
            <Button
              onClick={handleOpenFilters}
              leftIcon={<FiFilter />}
              colorScheme="blue"
              variant="outline"
            >
              Filtros
            </Button>
          </Flex>
        </Flex>

        <Slide direction="right" in={isOpen} style={{ zIndex: 9999 }}>
          <Box
            ref={panelRef}
            position="fixed"
            top="50%"
            right="0"
            transform="translateY(-50%)"
            width="300px"
            bg="white"
            boxShadow="lg"
            borderRadius="md"
            marginRight={'20px'}
            p={4}
          >
            <IconButton
              aria-label="Fechar painel"
              icon={<CloseIcon />}
              size="sm"
              variant="ghost"
              onClick={onClose}
              mb={2}
            />
            <Text fontWeight="medium" fontSize="lg" mb={4}>
              Filtros
            </Text>

            <Text fontWeight="medium" fontSize="sm" mb={2}>
              Buscar
            </Text>
            <InputGroup mb={4}>
              <InputLeftElement pointerEvents="none">
                <SearchIcon color="gray.300" />
              </InputLeftElement>
              <Input
                placeholder="Nome, e‑mail ou telefone..."
                value={preSearchTerm}
                onChange={(e) => setPreSearchTerm(e.target.value)}
              />
            </InputGroup>

            <Text fontWeight="medium" fontSize="sm" mb={2}>
              Status
            </Text>
            <CheckboxGroup
              colorScheme="blue"
              value={preSelectedStatuses}
              onChange={(values: (string | number)[]) =>
                setPreSelectedStatuses(values)
              }
            >
              <VStack align="stretch" spacing={1} mb={4}>
                <Checkbox value="flow_triggered">Flow Acionado</Checkbox>
                <Checkbox value="abandoned">Abandonado</Checkbox>
                <Checkbox value="whatsapp_sent">WhatsApp Enviado</Checkbox>
                <Checkbox value="failed">Falhou</Checkbox>
              </VStack>
            </CheckboxGroup>

            <Text fontWeight="medium" fontSize="sm" mb={2}>
              Período
            </Text>
            <ReactDatePicker
              selected={preStartDate}
              startDate={preStartDate}
              endDate={preEndDate}
              onChange={(dates: [Date, Date]) => {
                const [start, end] = dates;
                setPreStartDate(start ?? null);
                setPreEndDate(end ?? null);
              }}
              selectsRange
              inline
              locale={pt}
              calendarClassName="chakra-datepicker"
            />

            <Flex mt={4} justify="flex-end" gap={2}>
              <Button variant="ghost" size="sm" onClick={handleClearFilters}>
                Limpar filtros
              </Button>
              <Button
                size="sm"
                bg="black"
                color="white"
                onClick={() => {
                  setSearchTerm(preSearchTerm);
                  setSelectedStatuses(preSelectedStatuses);
                  setStartDate(preStartDate);
                  setEndDate(preEndDate);
                  onClose();
                }}
              >
                Aplicar Filtros
              </Button>
            </Flex>
          </Box>
        </Slide>

        <Divider orientation="horizontal" mt={10} />

        <TableContainer overflowX="visible">
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Criado em</Th>
                <Th>Nome</Th>
                <Th>Telefone</Th>
                <Th>Email</Th>
                <Th>Status</Th>
                <Th>Mensagem de Erro</Th>
              </Tr>
            </Thead>
            <Tbody>
              {abandonedCartsData?.data?.map((cart) => {
                const { colorScheme, icon } = statusColorMap[cart.status] || {
                  colorScheme: 'gray',
                  icon: null,
                };
                return (
                  <Tr key={cart.id}>
                    <Td>
                      {new Date(cart.sourceCreatedAt).toLocaleDateString(
                        'pt-BR',
                      )}
                    </Td>
                    <Td>{cart.customerName}</Td>
                    <Td>{cart.phoneNumberId}</Td>
                    <Td>{cart.customerEmail}</Td>
                    <Td>
                      <Badge pb="0.5" colorScheme={colorScheme}>
                        {icon}
                        {translatedStatus[cart.status]}
                      </Badge>
                    </Td>
                    <Td>{cart.errorMessage}</Td>
                  </Tr>
                );
              })}
            </Tbody>
          </Table>
        </TableContainer>

        <Pagination
          initialPage={currentPage}
          onChangePage={(page) => setCurrentPage(page)}
          rowsPerPage={rowsPerPage}
          totalRows={abandonedCartsData?.meta.totalItems!}
          onChangeRowsPerPage={(r) => setRowsPerPage(r)}
          itemsLabel="clientes"
        />
      </Box>
    </LoadingScreen>
  );
};

function generateSalesReportData(automationSalesData: AutomationSalesData) {
  const totalMessagesSent = automationSalesData?.totalMessagesSent || 0;
  const receivedCount = automationSalesData?.received.count || 0;
  const readCount = automationSalesData?.read.count || 0;
  const engagedCount = automationSalesData?.engaged.count || 0;

  return [
    {
      'Total de disparos': totalMessagesSent,
      'Total de entregues': receivedCount,
      'Total de lidas': readCount,
      'Total de engajadas': engagedCount,
      'Total de erros': totalMessagesSent - receivedCount,

      'Taxa de entrega': NumberUtils.getPercentValue(
        receivedCount,
        totalMessagesSent || 1,
      ),
      'Taxa de leitura': NumberUtils.getPercentValue(
        readCount,
        receivedCount || 1,
      ),
      'Taxa de engajamento': NumberUtils.getPercentValue(
        engagedCount,
        receivedCount || 1,
      ),

      'Total de pedidos (entregues)': automationSalesData?.received.totalOrders,
      'Total em vendas (entregues)': MoneyUtils.formatCurrency(
        automationSalesData?.received.totalOrdersValue,
      ),
      'ROI (entregues)': automationSalesData?.received.roi,

      'Total de pedidos (engajadas)': automationSalesData?.engaged.totalOrders,
      'Total em vendas (engajadas)': MoneyUtils.formatCurrency(
        automationSalesData?.engaged.totalOrdersValue,
      ),
      'ROI (engajadas)': automationSalesData?.engaged.roi,
    },
  ];
}

const BackgroundAutomationDetailsPage = () => {
  const { automationId } = useParams();
  const [startDate, setStartDate] = useState<Date>(startOfMonth(new Date()));
  const [endDate, setEndDate] = useState<Date>(endOfMonth(new Date()));
  const [funnelMetric, setFunnelMetric] =
    useState<FunnelMetric>('totalOrdersValue');
  const { downloadExcelFromJson } = useDownloadFile();

  const { data: automation } = useQuery(
    apiRoutes.showAutomation(automationId!),
    async () => {
      const { data } = await AutomationsService.showAutomation(automationId!);
      return data;
    },
  );
  const { data: automationSalesData } = useQuery(
    [
      'statistics-service',
      'automation-sales',
      automationId,
      startDate,
      endDate,
    ],
    async () => {
      const { data } = await StatisticsService.getChartAutomationSales(
        automationId || '',
        startDate,
        endDate,
      );
      return data;
    },
    {},
  );

  const { data: automationPerformanceData = [] } = useQuery(
    [
      'statistics-service',
      'automation-performance',
      automationId,
      startDate,
      endDate,
    ],
    async () => {
      const { data } = await StatisticsService.getChartAutomationPerformance(
        automationId || '',
        startDate,
        endDate,
      );
      return data;
    },
    {},
  );

  function handleClickExportAutomationSales() {
    const reportData = generateSalesReportData(automationSalesData!);
    downloadExcelFromJson(
      reportData,
      'relatorio-de-performance-da-automacao.xlsx',
    );
  }

  return (
    <Box>
      <Box paddingX={'100px'} paddingY="20px">
        <BreadcrumbBase
          items={[
            {
              path: appPaths.automations.backgroundAutomations.index(),
              name: 'Automações',
            },
            {
              path: appPaths.automations.backgroundAutomations.details(
                automationId || '',
              ),
              name: automation?.name || '',
            },
          ]}
        />
        <Flex alignItems={'flex-end'} flexDir="column" gap={2}>
          <Flex gap={10} alignItems="center">
            <Flex gap={2} alignItems="center">
              <Text>Início</Text>
              <ReactDatePicker
                isClearable
                selected={startDate}
                onSelect={(date: Date) => setStartDate(date)}
                onChange={(date: Date) => setStartDate(date)}
                dateFormat="dd/MM/yyyy"
              />
            </Flex>
            <Flex gap={2} alignItems="center">
              <Text>Fim</Text>
              <ReactDatePicker
                isClearable
                selected={endDate}
                onSelect={(date: Date) => setEndDate(date)}
                onChange={(date: Date) => setEndDate(date)}
                dateFormat="dd/MM/yyyy"
              />
            </Flex>

            <Button
              bgColor={colors.primary}
              color={colors.white}
              leftIcon={<TbTableExport size={20} />}
              onClick={handleClickExportAutomationSales}
            >
              Exportar
            </Button>
          </Flex>
        </Flex>
        <Heading>Conversões no período</Heading>
        <CustomECharts
          chartWidth={'100%'}
          chartHeight="450px"
          marginTop={'50px'}
          option={{
            legend: {},
            dataZoom: [
              {
                type: 'slider',
                start: 0,
                end: 50,
              },
            ],
            tooltip: {},
            color: [colors.primaryLight, colors.green],
            dataset: {
              dimensions: ['datetime', 'messages_count', 'orders_count'],
              source: automationPerformanceData,
            },
            xAxis: {
              type: 'category',
            },
            yAxis: [
              {
                name: 'Mensagens',
                nameLocation: 'middle',
                splitLine: { show: false },
                nameGap: 55,
              },
            ],
            series: [
              { type: 'line', name: 'Mensagens enviadas', yAxisIndex: 0 },
              {
                type: 'line',
                name: 'Mensagens convertidas em venda',
                yAxisIndex: 0,
              },
            ],
          }}
        />
        <Flex justifyContent="space-between" gap={3} mt={5}>
          {[
            {
              icon: <BsPeopleFill />,
              title: 'Disparos',
              value: automationSalesData?.totalMessagesSent,
              bgIconColor: colors.secondary,
              tooltip: 'Quantidade de Disparos',
            },
            {
              icon: <BsFillEyeFill />,
              title: 'Lidas',
              value: automationSalesData?.read.count,
              bgIconColor: colors.primaryLight,
              tooltip: 'Quantidade de mensagens lidas',
            },
            {
              icon: <HiCursorClick />,
              title: 'Cliques',
              value: automationSalesData?.engaged.count,
              bgIconColor: colors.green,
              tooltip: 'Quantidade de cliques no link',
            },
            {
              icon: <FaBox />,
              title: 'Pedidos',
              value: automationSalesData?.received.totalOrders,
              bgIconColor: colors.blueTurquose,
              tooltip: 'Quantidade de conversões',
            },
            {
              icon: <FaFunnelDollar />,
              title: 'Conversão',
              value: NumberUtils.getPercentValue(
                automationSalesData?.received.totalOrders,
                automationSalesData?.received.count,
              ),
              bgIconColor: colors.red,
              tooltip:
                'Percentual de mensagens entregues converteram em vendas',
            },
          ].map(({ icon, title, value, bgIconColor, tooltip }) => (
            <CardStatistic
              value={value}
              key={title}
              icon={icon}
              title={title}
              bgIconColor={bgIconColor}
              tooltip={tooltip}
            />
          ))}
        </Flex>
        <Heading marginTop={'50px'}>
          Performance da automação{' '}
          <Tooltip
            label={
              'Estimativa considerando compras realizadas até 3 dias após o envio da mensagem'
            }
          >
            <QuestionIcon boxSize={3} />
          </Tooltip>
        </Heading>
        <Heading size="xs" color={colors.fontlightGrey}>
          {startDate &&
            endDate &&
            `${format(startDate, 'dd/MM/yyyy')} à ${format(
              endDate,
              'dd/MM/yyyy',
            )}`}
        </Heading>
        <CustomECharts
          chartWidth={'100%'}
          chartHeight="450px"
          topRightComponent={
            <Select
              defaultValue={funnelMetric}
              onChange={(e) => setFunnelMetric(e.target.value as FunnelMetric)}
            >
              <option value="totalOrdersValue">Total em vendas</option>
              <option value="totalOrders">Total de pedidos</option>
              <option value="roi">ROI</option>
            </Select>
          }
          option={{
            title: {
              text: 'ENGAJOU = CLICOU OU RESPONDEU',
              borderColor: '#d0d0d0',
              borderWidth: 1,
              textStyle: {
                fontSize: 10,
                color: '#d0d0d0',
              },
              left: '80%',
              top: '90%',
            },
            color: [colors.orange, colors.primaryLight, colors.green],
            series: [
              {
                type: 'funnel',
                name: 'Outer',
                width: '80%',
                label: {
                  show: true,
                  fontSize: 14,
                },
                labelLine: {
                  length: 15,
                  lineStyle: {
                    width: 2,
                    type: 'solid',
                  },
                },
                itemStyle: {
                  borderColor: '#fff',
                  borderWidth: 2,
                },
                emphasis: {
                  disabled: true,
                },
                data: [
                  {
                    value: automationSalesData?.received[funnelMetric],
                    name: `RECEBEU A\nMENSAGEM (${automationSalesData?.received.count})`,
                  },
                  {
                    value: automationSalesData?.read[funnelMetric],
                    name: `LEU A MENSAGEM (${automationSalesData?.read.count})`,
                  },
                  {
                    value: automationSalesData?.engaged[funnelMetric],
                    name: `ENGAJOU COM A MENSAGEM (${automationSalesData?.engaged.count})`,
                  },
                ],
              },
              {
                type: 'funnel',
                name: 'Inner',
                width: '80%',
                label: {
                  show: true,
                  position: 'inside',
                  fontSize: 14,
                },
                labelLine: {
                  length: 20,
                  lineStyle: {
                    width: 2,
                    type: 'solid',
                  },
                },
                itemStyle: {
                  borderColor: '#fff',
                  borderWidth: 2,
                },
                emphasis: {
                  label: {
                    fontSize: 20,
                  },
                },
                data: [
                  {
                    value: automationSalesData?.received[funnelMetric],
                    name: ChartUtils.getFormattedFunnelValue(
                      automationSalesData?.received[funnelMetric],
                      funnelMetric,
                    ),
                  },
                  {
                    value: automationSalesData?.read[funnelMetric],
                    name: ` ${ChartUtils.getFormattedFunnelValue(
                      automationSalesData?.read[funnelMetric],
                      funnelMetric,
                    )} `, // blank space added to have different color on chart
                  },
                  {
                    value: automationSalesData?.engaged[funnelMetric],
                    name: `  ${ChartUtils.getFormattedFunnelValue(
                      automationSalesData?.engaged[funnelMetric],
                      funnelMetric,
                    )}  `, // blank space added to have different color on chart
                  },
                ],
              },
            ],
          }}
        />
        <Flex justifyContent="space-between" gap={3} mt={5}>
          {[
            {
              icon: <MdAttachMoney />,
              title: 'Receita gerada',
              value: ChartUtils.getFormattedFunnelValue(
                automationSalesData?.received.totalOrdersValue,
                'totalOrdersValue',
              ),
              bgIconColor: colors.greenMedium,
              tooltip:
                'Receita gerada por vendas de Clientes que Receberam a Mensagem',
            },
            {
              icon: <GiPayMoney />,
              title: 'Custo dos disparos',
              value: MoneyUtils.formatCurrency(
                automationSalesData?.automationCost || 0,
              ),
              bgIconColor: colors.danger,
              tooltip: 'Total de Mensagens Entregues * Custo por Mensagem',
            },
            {
              icon: <AiOutlineAreaChart />,
              title: 'ROI',
              value: ChartUtils.getFormattedFunnelValue(
                automationSalesData?.received.roi,
                'roi',
              ),
              bgIconColor: colors.primaryMedium,
              tooltip:
                'Receita Gerada / (Total de Mensagens Entregues * Custo por Mensagem)',
            },
          ].map(({ icon, title, value, bgIconColor, tooltip }) => (
            <CardStatistic
              value={value}
              key={title}
              icon={icon}
              title={title}
              bgIconColor={bgIconColor}
              tooltip={tooltip}
            />
          ))}
        </Flex>
        {automation &&
          automation?.automationType.slug ===
            AutomationTypeSlug.abandoned_cart && <AbandonedCartsTable />}
      </Box>
    </Box>
  );
};

export default BackgroundAutomationDetailsPage;
