import {
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  Button,
  IconButton,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Td,
  Text,
  Tr,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { FaEdit, FaRegCopy, FaTrashAlt } from 'react-icons/fa';
import { useMutation, useQueryClient } from 'react-query';
import ButtonIcon from '../../../../../../components/ButtonIcon';
import { colors } from '../../../../../../constants/colors';
import { apiRoutes } from '../../../../../../constants/api-routes';
import { EmailTemplate } from '../../../../../../types/Prisma';
import { EmailTemplatesService } from '../../../../../../services/email-templates.service';
import EmailTemplatePreview from '../../../../../../components/EmailTemplatePreview';
import { IoEyeOutline } from 'react-icons/io5';
import PopoverTestEmailTemplate from '../../PopoverTestEmailTemplate';
import { FiSend } from 'react-icons/fi';

interface RowProps {
  template: EmailTemplate;
  onClickEditTemplate: (template: EmailTemplate) => void;
  onClickDuplicateTemplate: (template: EmailTemplate) => void;
}

const Row = ({
  template,
  onClickEditTemplate,
  onClickDuplicateTemplate,
}: RowProps) => {
  const { t } = useTranslation();

  const {
    isOpen: isTestTemplatePopoverOpen,
    onOpen: onOpenTestTemplatePopover,
    onClose: onCloseTestTemplatePopover,
  } = useDisclosure();
  const {
    isOpen: isEmailTemplateDeleteConfirmationOpen,
    onOpen: onOpenEmailTemplateDeleteConfirmation,
    onClose: onCloseEmailTemplateDeleteConfirmation,
  } = useDisclosure();
  const queryClient = useQueryClient();
  const toast = useToast();
  const cancelEmailTemplateDeleteRef = React.useRef<HTMLButtonElement | null>(
    null,
  );

  const deleteMessageTemplate = useMutation(
    (templateId: string) =>
      EmailTemplatesService.deleteEmailTemplate(templateId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(apiRoutes.listEmailTemplates());
        onCloseEmailTemplateDeleteConfirmation();
        toast({
          title: 'Template excluído com sucesso',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      },
      onError: (err: any) => {
        toast({
          title: err.message,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      },
    },
  );

  async function handleClickedDeleteTemplate() {
    await deleteMessageTemplate.mutateAsync(template.id);
  }

  function getMenuButtons(): JSX.Element[] {
    return [
      <ButtonIcon
        icon={<FaEdit fontSize="20px" color={colors.darkGrey} />}
        onClick={() => onClickEditTemplate(template)}
      />,
      <ButtonIcon
        icon={<FaRegCopy fontSize="20px" color={colors.darkGrey} />}
        onClick={() => onClickDuplicateTemplate(template)}
      />,
      <PopoverTestEmailTemplate
        isOpen={isTestTemplatePopoverOpen}
        onOpen={onOpenTestTemplatePopover}
        onClose={onCloseTestTemplatePopover}
        template={template}
      >
        <IconButton
          aria-label="Send message"
          icon={<FiSend fontSize="20px" color={colors.darkGrey} />}
          backgroundColor={'transparent'}
        />
      </PopoverTestEmailTemplate>,
      <ButtonIcon
        icon={<FaTrashAlt fontSize="20px" color={colors.danger} />}
        onClick={onOpenEmailTemplateDeleteConfirmation}
      />,
    ];
  }

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedTemplate, setSelectedTemplate] =
    React.useState<EmailTemplate | null>(null);

  const handlePreviewClick = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    onOpen();
  };

  return (
    <Tr key={template.id} color={colors.black}>
      <Td fontWeight="bold">{template.name}</Td>
      <Td>
        <Text display="block" whiteSpace={'pre-wrap'}>
          {template.subject}
        </Text>
      </Td>
      <Td>
        <Button
          leftIcon={<IoEyeOutline size={16} />}
          size="sm"
          onClick={() => handlePreviewClick(template)}
        >
          Preview
        </Button>
      </Td>
      <Td>
        {getMenuButtons()}
        <AlertDialog
          isOpen={isEmailTemplateDeleteConfirmationOpen}
          leastDestructiveRef={cancelEmailTemplateDeleteRef}
          onClose={onCloseEmailTemplateDeleteConfirmation}
        >
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Confirmação de Exclusão
              </AlertDialogHeader>

              <AlertDialogBody>
                {`Você tem certeza de que deseja excluir o template de mensagem
                ${template.name}?\n`}
                <p>Atenção: Esta ação é irreversível</p>
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  ref={cancelEmailTemplateDeleteRef}
                  onClick={onCloseEmailTemplateDeleteConfirmation}
                >
                  Cancelar
                </Button>
                <Button
                  colorScheme="red"
                  onClick={handleClickedDeleteTemplate}
                  ml={3}
                  isLoading={deleteMessageTemplate.isLoading}
                >
                  Confirmar
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Td>

      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent maxW="440px">
          <ModalHeader>Email Preview</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {selectedTemplate && (
              <EmailTemplatePreview
                emailTemplate={selectedTemplate}
                selectedEmailTemplate={selectedTemplate}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Tr>
  );
};

export default Row;
