import { <PERSON>, Table<PERSON>ontainer, <PERSON>body, Th, Thead, Tr } from '@chakra-ui/react';
import { useQuery } from 'react-query';
import LoadingScreen from '../../../../../components/LoadingScreen';
import { apiRoutes } from '../../../../../constants/api-routes';
import { useNavigate } from 'react-router-dom';
import Row from './Row';
import { EmailTemplate } from '../../../../../types/Prisma';
import { EmailTemplatesService } from '../../../../../services/email-templates.service';
import { appPaths } from '../../../../../constants/app-paths';

const TableEmailTemplates = () => {
  const { data: templates, isLoading: isLoadingTemplates } = useQuery(
    apiRoutes.listEmailTemplates(),
    async () => {
      const { data } = await EmailTemplatesService.listEmailTemplates();
      return data;
    },
    {},
  );

  const navigate = useNavigate();

  function handleClickedEdit(emailTemplate: EmailTemplate) {
    navigate(
      appPaths.messageTemplates.email.editEmailTemplate(emailTemplate.id),
    );
  }

  function handleClickDuplicate(emailTemplate: EmailTemplate) {
    navigate(appPaths.messageTemplates.email.createEmailTemplate(), {
      state: { emailTemplate },
    });
  }

  function getRows(): JSX.Element[] {
    return templates
      ? templates.map((template, index) => {
          return (
            <Row
              key={template.id}
              template={template}
              onClickEditTemplate={handleClickedEdit}
              onClickDuplicateTemplate={handleClickDuplicate}
            />
          );
        })
      : [];
  }

  return (
    <LoadingScreen isLoading={isLoadingTemplates}>
      <TableContainer>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Nome</Th>
              <Th>Assunto do Email</Th>
              <Th>Preview</Th>
              <Th>Ações</Th>
            </Tr>
          </Thead>
          <Tbody>{getRows()}</Tbody>
        </Table>
      </TableContainer>
    </LoadingScreen>
  );
};

export default TableEmailTemplates;
