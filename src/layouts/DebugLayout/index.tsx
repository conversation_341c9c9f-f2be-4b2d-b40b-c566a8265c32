import { Grid, GridItem } from '@chakra-ui/react';
import { ReactNode, useEffect } from 'react';
import SidebarSecondary from '../../components/SidebarSecondary';
import { appPaths } from '../../constants/app-paths';
import { FiMessageSquare, FiLock, FiShield } from 'react-icons/fi';

interface SettingsLayoutProps {
  children: ReactNode;
}

const SIDEBAR_OPTIONS = [
  {
    title: 'Simulador de recebimento de mensagens',
    path: appPaths.debug.simulateMessageReceiveDebugPage(),
    icon: FiMessageSquare,
  },
  {
    title: 'Assumir identidade de usuário (Impersonate)',
    path: appPaths.debug.impersonateDebugPage(),
    icon: FiLock,
  },
  {
    title: 'Minhas Permissões de App',
    path: appPaths.debug.myAppModulesPermissionsDebugPage(),
    icon: FiShield,
  },
];

const DebugLayout = ({ children }: SettingsLayoutProps) => {
  const isDebugToolsEnabled =
    process.env.REACT_APP_ENABLE_DEBUG_TOOLS === 'true';

  useEffect(() => {
    if (!isDebugToolsEnabled) {
      window.location.assign(appPaths.home());
    }
  }, []);

  if (!isDebugToolsEnabled) {
    return null;
  }

  return (
    <Grid
      height="100vh"
      templateColumns="auto 1fr"
      templateAreas={'"sidebar page"'}
    >
      <GridItem area="sidebar">
        <SidebarSecondary
          title="Ferramentas de debug"
          options={SIDEBAR_OPTIONS}
        />
      </GridItem>
      <GridItem area="page" maxH={'100vh'} height={'100vh'} overflow="auto">
        {children}
      </GridItem>
    </Grid>
  );
};

export default DebugLayout;
