import { ChakraProvider, useToast } from '@chakra-ui/react';
import { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import { CrudAutoSortingModalProvider } from './hooks/useCrudAutoSortingModal';
import { SocketProvider } from './hooks/useSocket';
import Routes from './routes';
import { checkAuthentication } from './state/authSlice';
import { hideToast } from './state/globalSlice';
import { RootState } from './state/store';
import './i18n';
import EmojiDataSetProvider from './contexts/EmojiDataSetContext';
import customTheme from './theme';

const DEFAULT_EMOJI_DATA_SET_LOCALE = 'pt';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // default: true
    },
  },
});
function App() {
  const dispatch = useDispatch();
  const showToastState = useSelector(
    (state: RootState) => state.global.showToast,
  );
  const toastOptions = useSelector(
    (state: RootState) => state.global.toastOptions,
  );
  const toast = useToast();

  useEffect(() => {
    if (showToastState && toastOptions) {
      toast(toastOptions);
      dispatch(hideToast());
    }
  }, [showToastState, toastOptions, toast, dispatch]);

  useEffect(() => {
    window.addEventListener('storage', (event) => {
      dispatch(checkAuthentication());
    });
    dispatch(checkAuthentication());

    return () => {
      window.removeEventListener('storage', (event) => {});
    };
  }, [dispatch]);

  return (
    <div className="App">
      <ChakraProvider theme={customTheme}>
        <SocketProvider>
          <QueryClientProvider client={queryClient}>
            <CrudAutoSortingModalProvider>
              <EmojiDataSetProvider locale={DEFAULT_EMOJI_DATA_SET_LOCALE}>
                <Router>
                  <Routes />
                </Router>
              </EmojiDataSetProvider>
            </CrudAutoSortingModalProvider>
          </QueryClientProvider>
        </SocketProvider>
      </ChakraProvider>
    </div>
  );
}

export default App;
