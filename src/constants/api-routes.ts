import {
  GetConversationIdsWithUnreadMessagesParams,
  GetConversationsCountParams,
  ListConversationsDetailedParams,
  ListConversationsParams,
  ListConversationSummaryParams,
} from '../services/conversations.service';
import { ListCustomersParams } from '../services/customers.service';
import { DownloadMediaDto } from '../services/messages.service';
import { GetTopCampaignsOrderBy } from '../services/statistics.service';
import { CompanyDefinedFieldTableEnum } from '../types/CompanyDefinedField';
import { MessageStatus } from '../types/Message';
import {
  FlowType,
  MessageTemplateStatus,
  MessageTemplateType,
  SourceIntegration,
} from '../types/Prisma';
import { DateUtils } from '../utils/date.utils';
import { UrlUtils } from '../utils/url.utils';

export const apiRoutes = {
  // auth
  login: () => '/auth/login',
  updatePassword: () => '/auth/update-password',
  updatePasswordWithToken: () => '/auth/update-password-with-token',

  // bling
  getBlingAuthUrl: () => '/bling/auth',

  // conversations
  listConversations: (listConversationParams: ListConversationsParams) => {
    const queryParams: { [key: string]: string | undefined } =
      listConversationParams as any;
    const queryString = UrlUtils.convertObjectToQueryString(queryParams);
    return `/conversations?${queryString}`;
  },
  listConversationsDetailed: (
    listConversationsDetailedParams: ListConversationsDetailedParams,
  ) => {
    const queryParams: { [key: string]: string | undefined } =
      listConversationsDetailedParams as any;
    const queryString = UrlUtils.convertObjectToQueryString(queryParams);
    return `/conversations/detailed?${queryString}`;
  },
  showConversation: (conversationId: string) =>
    `/conversations/${conversationId}`,
  updateConversation: (conversationId: string) =>
    `/conversations/${conversationId}`,
  createConversation: () => '/conversations',
  readConversation: (conversationId: string) =>
    `/conversations/${conversationId}/read`,
  closeAllConversationTickets: (
    conversationId: string,
    triggerCsatFlow: boolean,
  ) =>
    `/conversations/${conversationId}/close-all-tickets?triggerCsatFlow=${triggerCsatFlow}`,
  closeAllTicketsFromManyConversations: (conversationIds: string[]) =>
    `/conversations/close-all-tickets?conversationIds=${conversationIds.join(',')}`,
  listConversationsCustomers: () => '/conversations/customers',

  getConversationsCount: (params: GetConversationsCountParams) =>
    `/conversations/count?ticketStatus=${params.ticketStatus}&daysSinceLastMessage=${params.daysSinceLastMessage}`,
  getConversationIdsWithUnreadMessages: (
    params: GetConversationIdsWithUnreadMessagesParams,
  ) =>
    `/conversations/unread-messages?ticketStatus=${params.ticketStatus}&daysSinceLastMessage=${params.daysSinceLastMessage}`,
  listConversationSummary: (params: ListConversationSummaryParams) =>
    `/conversations/summary?daysSinceLastMessage=${params.daysSinceLastMessage}`,
  markConversationAs: (conversationId: string, state: MessageStatus) =>
    `/conversations/${conversationId}/mark/${state}`,

  // conversation tickets
  updateConversationTicket: (conversationTicketId: string) =>
    `/conversation-tickets/${conversationTicketId}`,

  // messages
  listMessagesByConversation: (conversationId: string, page: number) =>
    `/conversations/${conversationId}/messages?page=${page}`,
  sendMessage: () => '/messages/send',
  sendMessageTemplate: () => '/messages/send-template',
  sendMessageTemplateByPhone: () => '/messages/send-template-by-phone',
  uploadMedia: () => '/messages/upload-media',

  // conversation categories
  listConversationCategories: () => '/conversation-categories',
  listConversationCategoriesDetailed: (daysSinceLastMessage?: number) => {
    const queryParams: { [key: string]: string | undefined } = {
      daysSinceLastMessage: daysSinceLastMessage?.toString(),
    };
    const queryString = UrlUtils.convertObjectToQueryString(queryParams);
    return `/conversation-categories/detailed${queryString ? `?${queryString}` : ''}`;
  },
  createConversationCategory: () => '/conversation-categories',
  updateConversationCategory: (conversationCategoryId: string) =>
    `/conversation-categories/${conversationCategoryId}`,
  deleteConversationCategory: (conversationCategoryId: string) =>
    `/conversation-categories/${conversationCategoryId}`,
  updateConversationCategoryPosValue: (conversationCategoryId: string) =>
    `/conversation-categories/${conversationCategoryId}/update-pos`,

  // conversation sectors
  listConversationSectors: () => '/conversation-sectors',
  listMyConversationSectors: () => '/conversation-sectors/my-sectors',
  createConversationSector: () => '/conversation-sectors',
  getConversationSector: (conversationSectorId: string) =>
    `/conversation-sectors/${conversationSectorId}`,
  updateConversationSector: (conversationSectorId: string) =>
    `/conversation-sectors/${conversationSectorId}`,
  deleteConversationSector: (conversationSectorId: string) =>
    `/conversation-sectors/${conversationSectorId}`,
  hasSomeConversationSectorAssociatedToUser: (userId: string) =>
    '/conversation-sectors/my-sectors/has-sector',

  // whatsapp
  downloadMedia: ({ mediaId, mediaName, fileKey }: DownloadMediaDto) =>
    `/whatsapp/file?${mediaId ? `fileId=${mediaId}` : ''}${
      mediaName ? `&fileName=${mediaName}` : ''
    }${fileKey ? `&fileKey=${fileKey}` : ''}`,
  // whatsapp
  sendMedia: () => '/whatsapp/file',

  // statistics
  getChartCampaignPerformance: (campaignId: string, timeUnit: 'hour' | 'day') =>
    `/statistics/charts/campaign-performance?campaignId=${campaignId}&timeUnit=${timeUnit}`,
  getChartEmailCampaignPerformance: (
    emailCampaignId: string,
    timeUnit: 'hour' | 'day',
  ) =>
    `/statistics/charts/email-campaign-performance?emailCampaignId=${emailCampaignId}&timeUnit=${timeUnit}`,
  getChartCampaignSales: (campaignId: string) =>
    `/statistics/charts/campaign-sales?campaignId=${campaignId}`,
  getChartEmailCampaignSales: (emailCampaignId: string) =>
    `/statistics/charts/email-campaign-sales?emailCampaignId=${emailCampaignId}`,
  getChartAutomationSales: (
    automationId: string,
    startDate: Date,
    endDate: Date,
  ) =>
    `/statistics/charts/automation-sales?automationId=${automationId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
  getChartAutomationPerformance: (
    automationId: string,
    startDate: Date,
    endDate: Date,
  ) =>
    `/statistics/charts/automation-performance?automationId=${automationId}&startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
  getOrderAggByCustomerStatistics: (
    startOrdersCreatedAt?: string,
    endOrdersCreatedAt?: string,
  ) =>
    `/statistics/orders-agg-by-customer?${
      startOrdersCreatedAt
        ? `startOrdersCreatedAt=${startOrdersCreatedAt}&`
        : ''
    }${endOrdersCreatedAt ? `endOrdersCreatedAt=${endOrdersCreatedAt}&` : ''}`,
  getTotalWhatsappCampaigns: (startDate: Date, endDate: Date) =>
    `statistics/total-whatsapp-campaigns?startDate=${startDate}&endDate=${endDate}`,
  getTotalMesssagesSent: (startDate: Date, endDate: Date) =>
    `statistics/total-messsages-sent?startDate=${startDate}&endDate=${endDate}`,
  getMessagesBounceRate: (startDate: Date, endDate: Date) =>
    `statistics/messages-bounce-rate?startDate=${startDate}&endDate=${endDate}`,
  getMessagesEngagementRate: (startDate: Date, endDate: Date) =>
    `statistics/messages-engagement-rate?startDate=${startDate}&endDate=${endDate}`,
  getReviRevenueSummary: () => 'statistics/revenue/revi/summary',
  getReviRevenueSummaryByMonth: () =>
    'statistics/revenue/revi/summary/by-month',
  getCustomersKpiSummary: (startDate: Date, endDate: Date) =>
    `statistics/customers/kpi/summary?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`,
  getRFMAnalysis: (maxRecency: number) =>
    `statistics/customers/rfm/summary?maxRecency=${maxRecency}`,
  getRFMAnalysisHistory: (numberOfPreviousMonths: number, maxRecency: number) =>
    `statistics/customers/rfm/summary/history?numberOfPreviousMonths=${numberOfPreviousMonths}&maxRecency=${maxRecency}`,
  getOrdersCountByWeekDayAndHour: () =>
    '/statistics/orders/summary/agg-by-week-day-and-hour',
  getTopCampaigns: (orderBy: GetTopCampaignsOrderBy, limit: number = 10) =>
    `/statistics/campaigns/top?orderBy=${orderBy}&limit=${limit}`,
  getEmailCampaignsSummary: () => 'statistics/email-campaigns/summary',
  getCustomersPurchaseFrequencySummary: () =>
    'statistics/customers/purchase-frequency/summary',
  getCustomersRfmGroupTransitions: () =>
    'statistics/customers/rfm/group-transitions',
  getOrdersCohortAnalysis: () => 'statistics/orders/cohort-analysis',

  // auto-sorting-option
  listAutoSortingOptions: () => '/automatic-sorting-options',
  createAutoSortingOption: () => '/automatic-sorting-options',
  updateAutoSortingOption: (autoSortingOptionId: string) =>
    `/automatic-sorting-options/${autoSortingOptionId}`,
  deleteAutoSortingOption: (autoSortingOptionId: string) =>
    `/automatic-sorting-options/${autoSortingOptionId}`,

  // auto-replies
  listAutoReplies: () => '/automatic-replies',
  createAutoReply: () => '/automatic-replies',
  updateAutoReply: (autoReplyId: string) => `/automatic-replies/${autoReplyId}`,
  deleteAutoReply: (autoReplyId: string) => `/automatic-replies/${autoReplyId}`,

  // api-keys
  getApiKey: (integration: SourceIntegration, name: string) =>
    `/api-keys?integration=${integration}&name=${name}`,
  createApiKey: () => '/api-keys',
  generateNewApiKey: (apiKeyId: string) => `/api-keys/regenerate/${apiKeyId}`,

  // companies
  getCompanyDetails: () => '/companies/details',
  updateCompany: (companyId: string) => `/companies/${companyId}`,
  getCompanyMessageBalance: () => '/companies/message-balance',
  getCompanyMessageBalanceByCategory: () =>
    '/companies/message-balance/categories',
  showPhoneQualityRating: () => 'companies/phone-quality-rating',
  getCompanyBusinessHours: () => '/companies/business-hours',
  updateCompanyBusinessHours: () => '/companies/business-hours',
  getCompanyBillingSettings: () => '/companies/billing-settings',
  toggleDefaultDisabledColumn: (columnId: string) =>
    `/companies/company-default-fields/toggle?fieldId=${columnId}`,

  // company-recommendations
  listCompanyRecommendations: () => '/recommendations',

  // company-defined-fields
  listCompanyDefinedFields: (table: CompanyDefinedFieldTableEnum) =>
    `/company-defined-fields?table=${table}`,
  listValuesInCompanyDefinedField: (companyDefinedField: string) =>
    `/company-defined-fields/custom-field-values?companyDefinedField=${companyDefinedField}`,
  createCompanyDefinedField: () => '/company-defined-fields',
  toggleColumnIsActive: (companyDefinedFieldId: string) =>
    `/company-defined-fields/${companyDefinedFieldId}/toggle/is-active`,
  deleteCompanyDefinedField: (companyDefinedFieldId: string) =>
    `/company-defined-fields/${companyDefinedFieldId}`,

  // message-templates
  showMessageTemplate: (id: string) => `/message-templates/${id}`,
  listMessageTemplates: (params?: {
    ids?: string;
    type?: MessageTemplateType;
    status?: MessageTemplateStatus;
    communicationChannel?: string;
    usedInCampaigns?: boolean;
  }) => {
    const { ids, type, status, communicationChannel, usedInCampaigns } =
      params || {};
    const queryParams: { [key: string]: string | boolean | undefined } = {
      ids,
      type,
      status,
      communicationChannel,
      usedInCampaigns,
    };

    const queryString = UrlUtils.convertObjectToQueryString(queryParams);
    return `/message-templates${queryString ? `?${queryString}` : ''}`;
  },
  listQuickReplyMessageTemplates: () => '/message-templates/quick-replies',
  createMessageTemplate: () => '/message-templates',
  generateMessageTemplate: () => '/message-templates/generate',
  regenerateMessageTemplate: () => '/message-templates/re-generate',
  updateMessageTemplate: (messageTemplateId: string) =>
    `/message-templates/${messageTemplateId}`,
  deleteMessageTemplate: (messageTemplateId: string) =>
    `/message-templates/${messageTemplateId}`,
  getMessageTemplateCreateDto: (id: string) =>
    `/message-templates/get-create-dto/${id}`,

  // email-templates
  listEmailTemplates: () => '/email-templates',
  deleteEmailTemplate: (emailTemplateId: string) =>
    `/email-templates/${emailTemplateId}`,
  createEmailTemplate: () => '/email-templates',
  updateEmailTemplate: (emailTemplateId: string) =>
    '/email-templates/' + emailTemplateId,
  showEmailTemplate: (emailTemplateId: string) =>
    `/email-templates/${emailTemplateId}`,
  validateEmailTemplate: () => '/email-templates/validate',

  // email
  sendEmailTemplateTest: () => '/emails/send-email-template-test',

  // email-domains
  createEmailDomain: () => '/email-domains',
  listEmailDomains: () => '/email-domains',
  checkEmailDomainStatus: () => '/email-domains/verify-status',

  // customers
  uploadCustomers: () => '/customers/upload-file',
  bulkAddTagsToCustomers: () => '/customers/tags/bulk-add',
  bulkAssignDefaultAgentToCustomers: () =>
    '/customers/default-agent/bulk-assign',
  listCustomers: (
    listCustomersParams: ListCustomersParams = {
      // default values to invalidateQueries on reactQuery
      selectedEngagementActionTypes: undefined,
      excludedTemplateIds: undefined,
      selectedTemplateId: undefined,
      page: undefined,
      perPage: undefined,
    },
  ) => {
    const queryParams: { [key: string]: string | undefined } =
      listCustomersParams as any;

    const queryString = UrlUtils.convertObjectToQueryString(queryParams);
    return `/customers?${queryString}`;
  },
  toggleOptOutCustomer: (customerId: string) =>
    `/customers/${customerId}/toggle/opt-out`,
  deleteCustomers: () => '/customers/delete',
  listCustomerTableHeaders: () => '/customers/table-headers',
  listOptedOutCustomers: () => '/customers/opted-out',
  listCustomerStates: () => '/customers/states',
  listCustomerCities: (search?: string) =>
    `/customers/cities?search=${search ?? ''}`,
  listOrdersHistoryByCustomer: (customerId: string) =>
    `/customers/${customerId}/orders`,
  emailOptOut: (customerId: string) => `/customers/${customerId}/email-opt-out`,

  // debug
  debugSetMyAppModulesPermissions: () =>
    '/debug/me/set-my-app-modules-permissions',
  debugSimulateMessageReceive: () => '/debug/simulate-message-receive',
  debugListAvailableUsersForImpersonate: () =>
    '/debug/impersonate/list-available-users',
  debugImpersonate: () => '/debug/impersonate',

  // orders
  uploadOrders: () => '/orders/upload-file',
  listOrdersCoupons: () => '/orders/coupons',
  listOrdersStatuses: () => '/orders/statuses',

  // shopify
  syncShopifyOrders: () => '/shopify/sync-orders',

  // whatsapp-campaigns
  listWhatsappCampaigns: (
    page: number,
    perPage: number,
    templateIds?: string,
  ) =>
    `/whatsapp-campaigns/stats?page=${page}&perPage=${perPage}&templateIds=${templateIds}`,
  getWhatsappCampaignDetails: (campaignId: string) =>
    `/whatsapp-campaigns/${campaignId}/details`,
  sendOrScheduleWhatsappCampaign: () => '/whatsapp-campaigns/send-or-schedule',
  cancelWhatsappCampaign: (campaignId: string) =>
    `/whatsapp-campaigns/${campaignId}/cancel`,
  listCustomersOptOut: (campaignId: string) =>
    `/whatsapp-campaigns/${campaignId}/customers/opt-out`,

  // campaign-experiments
  startOrScheduleCampaignExperiment: () =>
    '/campaign-experiments/start-or-schedule',

  // sms-campaigns
  sendOrScheduleSmsCampaign: () => '/sms-campaigns/send-or-schedule',
  listSmsCampaigns: () => '/sms-campaigns/stats',
  cancelSmsCampaign: (campaignId: string) =>
    `/sms-campaigns/${campaignId}/cancel`,

  // sms-messages
  sendSmsTemplateTest: () => '/sms-messages/send-sms-template-test',

  // email-campaigns
  sendOrScheduleEmailCampaign: () => '/email-campaigns/send-or-schedule',
  listEmailCampaigns: (page: number, perPage: number) =>
    `/email-campaigns/stats?page=${page}&perPage=${perPage}`,
  cancelEmailCampaign: (emailCampaignId: string) =>
    `/email-campaigns/${emailCampaignId}/cancel`,
  getEmailCampaignDetails: (emailCampaignId: string) =>
    `/email-campaigns/${emailCampaignId}/details`,

  // message-template-suggestions
  listMessageTemplateSuggestions: () => '/message-template-suggestions',

  // tags
  listTags: (tagsIds?: string) => `/tags${tagsIds ? `?ids=${tagsIds}` : ''}`,
  createTag: () => '/tags',
  updateTag: (tagId: string) => `/tags/${tagId}`,
  deleteTags: (tagIds: string) => `/tags/${tagIds}`,

  // filters
  listFilters: () => '/filters',
  createFilter: () => '/filters',
  deleteFilter: (filterId: string) => `/filters/${filterId}`,
  updateFilter: (filterId: string) => `/filters/${filterId}`,

  // audience-recommendations
  listAudienceRecommendations: () => '/audience-recommendations',

  // quick-replies
  listQuickReplies: () => '/quick-replies',
  createQuickReply: () => '/quick-replies',
  updateQuickReply: (quickReplyId: string) => `/quick-replies/${quickReplyId}`,
  deleteQuickReply: (quickReplyId: string) => `/quick-replies/${quickReplyId}`,

  // flows
  createFlow: () => 'flows',
  copyFlow: (flowId: string) => `flows/${flowId}/copy`,
  updateFlow: (flowId: string) => `flows/${flowId}`,
  listFlows: () => 'flows',
  countFlows: (type?: FlowType, isActive?: boolean) => {
    const queryParams = new URLSearchParams();
    if (type !== undefined) queryParams.append('type', type);
    if (isActive !== undefined)
      queryParams.append('isActive', String(isActive));
    return `flows/count${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  },
  hasActiveFlow: (type?: FlowType) => {
    const queryParams = new URLSearchParams();
    if (type !== undefined) queryParams.append('type', type);
    return `flows/has-active-flow${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
  },
  showFlow: (flowId: string) => `flows/${flowId}`,
  deleteFlow: (flowId: string) => `flows/${flowId}`,

  // flow-node-buttons
  listFlowNodeButtons: (flowNodeId: string) =>
    `flow-node-buttons?flowNodeId=${flowNodeId}`,

  // products
  listProducts: () => 'products',

  // automations
  showAutomation: (automationId: string) => `automations/${automationId}`,
  showAutomationType: (automationTypeId: string) =>
    `automations/type/${automationTypeId}`,
  listAutomations: () => 'automations',
  listAutomationTypes: () => 'automations/types',
  createAutomation: () => 'automations',
  updateAutomation: (automationId: string) => `automations/${automationId}`,
  updateAbandonedCartAutomation: (automationId: string) =>
    `automations/${automationId}/abandoned-cart`,
  deleteAutomation: (automationId: string) => `automations/${automationId}`,
  toggleAutomation: (automationId: string) =>
    `automations/${automationId}/toggle`,
  createAutomationFromIntegration: () => 'automations/from-integration',
  updateAutomationFromIntegration: (automationId: string) =>
    `automations/${automationId}/from-integration`,

  // cashback-configs
  listCashbackConfig: (integration: string) =>
    `cashback-configs?integration=${integration}`,
  createCashbackConfig: () => 'cashback-configs',
  updateCashbackConfig: (cashbackConfigId: string) =>
    `cashback-configs/${cashbackConfigId}`,
  toggleCashback: (cashbackConfigId: string) =>
    `cashback-configs/${cashbackConfigId}/toggle`,

  // files
  getSignedUrl: () => 'files/signed-url',

  // reports
  exportFlowClicksReport: (flowId: string) =>
    `reports/flows/${flowId}/clicks/export`,
  getReportOverview: (startDate: string, endDate: string) =>
    `reports/overview?startDate=${startDate}&endDate=${endDate}`,
  exportCampaignSalesReport: (
    startDate?: string,
    endDate?: string,
    campaignIds?: string,
  ) => {
    const queryParams = {
      startDate,
      endDate,
      campaignIds,
    };
    const queryString = UrlUtils.convertObjectToQueryString(queryParams);
    return `reports/campaigns/sales/export${queryString ? `?${queryString}` : ''}`;
  },
  exportAttendanceMetricsReport: (startDate: string, endDate: string) =>
    `reports/attendance-metrics/export?startDate=${startDate}&endDate=${endDate}`,
  getStartedSessionsByHourAndDayOfWeekReport: (
    startDate: Date,
    endDate: Date,
  ) => {
    return `/reports/conversations/started-sessions-by-hour-and-day-of-week?startDate=${startDate}&endDate=${endDate}`;
  },
  getChatAgentMetricsReport: (startDate: Date, endDate: Date) => {
    if (!DateUtils.isValidDate(startDate) || !DateUtils.isValidDate(endDate))
      return '/reports/conversations/chat-agent-metrics';

    return `/reports/conversations/chat-agent-metrics?startDate=${startDate}&endDate=${endDate}`;
  },

  // users
  listUsers: () => 'users',
  listCompanyAgents: () => 'users/company-agents',

  // integrations
  getIntegrationStatusSummary: () => 'integrations/status/summary',

  getVtexConfig: () => 'integrations/vtex/config',
  registerVtexOrderHook: () => 'integrations/vtex/orders/hook/register',

  getShopifyConfig: () => 'integrations/shopify/config',
  saveShopifyCredentials: () => 'integrations/shopify/credentials',

  getBlingConfig: () => 'integrations/bling/config',
  saveBlingCredentials: () => 'integrations/bling/credentials',

  getCartPandaConfig: () => 'integrations/cart-panda/config',
  saveCartPandaCredentials: () => 'integrations/cart-panda/credentials',
  getUnboxConfig: () => 'integrations/unbox/config',
  saveUnboxCredentials: () => 'integrations/unbox/credentials',

  getVisualECommerceConfig: () => 'integrations/visual-ecommerce/config',
  saveVisualECommerceCredentials: () =>
    'integrations/visual-ecommerce/credentials',

  getMagazordConfig: () => 'integrations/magazord/config',
  saveMagazordCredentials: () => 'integrations/magazord/credentials',

  getMagentoConfig: () => 'integrations/magento/config',
  saveMagentoCredentials: () => 'integrations/magento/credentials',

  getYampiConfig: () => 'integrations/yampi/config',
  saveYampiCredentials: () => 'integrations/yampi/credentials',

  getOmieConfig: () => 'integrations/omie/config',
  saveOmieCredentials: () => 'integrations/omie/credentials',

  getOmnyConfig: () => 'integrations/omny/config',
  saveOmnyCredentials: () => 'integrations/omny/credentials',

  getTrayConfig: () => 'integrations/tray/config',
  saveTrayCredentials: () => 'integrations/tray/credentials',

  getLojaIntegradaConfig: () => 'integrations/loja-integrada/config',
  registerLojaIntegradaOrderHook: () =>
    'integrations/loja-integrada/orders/hook/register',

  saveLinxcommerceCredentials: () => 'integrations/linx-commerce/credentials',

  getNuvemShopConfig: () => 'integrations/nuvem-shop/config',
  saveNuvemShopCredentials: () => 'integrations/nuvem-shop/credentials',
  authorizeNuvemShopApp: () => 'nuvem-shop/authorize-app',

  getShoppubConfig: () => 'integrations/shoppub/config',
  saveShoppubCredentials: () => 'integrations/shoppub/credentials',
  // invoices
  listInvoices: () => 'invoices',

  getTinyConfig: () => 'integrations/tiny/config',
  saveTinyCredentials: () => 'integrations/tiny/credentials',

  getVndaConfig: () => 'integrations/vnda/config',
  saveVndaCredentials: () => 'integrations/vnda/credentials',

  getMillenniumConfig: () => 'integrations/millennium/config',
  saveMillenniumCredentials: () => 'integrations/millennium/credentials',

  getIntegrationsConfig: (sourceIntegration: SourceIntegration) =>
    `integrations/config?source=${sourceIntegration}`,

  saveIntegrationConfig: () => 'integrations',
  getVarejoOnlineAppInstallationUrl: () =>
    'integrations/varejo-online/app-installation-url',

  // agents routes
  getAIReplySuggestions: (conversationId: string) =>
    `webhook/conversation/agent/answer-suggestion?conversationId=${conversationId}`,

  // logs rotes
  createInfoLog: () => 'logs/create-info-log',
  // roles
  listRoles: () => 'roles',
  getRole: (roleId: string) => `roles/${roleId}`,
  createRole: () => 'roles',
  updateRole: (roleId: string) => `roles/${roleId}`,
  deleteRole: (roleId: string) => `roles/${roleId}`,

  // abandoned carts
  listAbandonedCarts: (params?: {
    perPage?: number;
    page?: number;
    startDate?: Date;
    endDate?: Date;
    status?: string;
    searchQuery?: string;
  }) => {
    const {
      perPage = 20,
      page = 1,
      startDate,
      endDate,
      status,
      searchQuery,
    } = params ?? {};

    const queryParams = new URLSearchParams();
    if (perPage) queryParams.append('perPage', perPage.toString());
    if (page) queryParams.append('page', page.toString());
    if (startDate) {
      const date = new Date(startDate);
      if (!isNaN(date.getTime())) {
        queryParams.append('startDate', date.toISOString());
      }
    }
    if (endDate) {
      const date = new Date(endDate);
      if (!isNaN(date.getTime())) {
        queryParams.append('endDate', date.toISOString());
      }
    }
    if (status) queryParams.append('status', status);
    if (searchQuery) queryParams.append('searchQuery', searchQuery);

    return `abandoned-carts?${queryParams.toString()}`;
  },
};
