import { UrlUtils } from '../utils/url.utils';
import { EngagementActionsEnum } from '../types/EngagementActionsEnum';
import { CustomerFiltersEnum } from '../types/CustomerFiltersEnum';

interface CustomersPageQueryParams
  extends Partial<Record<CustomerFiltersEnum, string | number>> {
  isSendTemplateModalOpen?: boolean;
  selectedEngagementActionTypes?: EngagementActionsEnum;
}

export function getCustomersPageQueryParams(
  queryParams: CustomersPageQueryParams,
): string {
  return UrlUtils.convertObjectToQueryString(queryParams);
}

export const appPaths = {
  home: () => '/home',
  updatePasswordWithToken: () => '/atualizar-senha',
  conversations: () => '/conversas',
  automations: {
    autoSorting: () => '/automacoes/triagem-inicial',
    autoReplies: () => '/automacoes/respostas-automaticas',
    messageFlows: {
      index: () => '/automacoes/fluxos-de-mensagem',
      editMessageFlow: (flowId: string) =>
        `/automacoes/fluxos-de-mensagem/${flowId}/editar`,
      copyAndEditMessageFlow: (flowId: string) =>
        `/automacoes/fluxos-de-mensagem/${flowId}/editar/copiar`,
    },
    cashback: () => '/automacoes/cashback',
    backgroundAutomations: {
      index: () => '/automacoes/envios-automatizados',
      // editAutomation: (automationId: string) => `/automacoes/envios-automatizados/${automationId}/editar`,
      createAutomation: () => '/automacoes/envios-automatizados/novo',
      editAutomation: (automationId: string) =>
        `/automacoes/envios-automatizados/${automationId}/editar`,
      details: (automationId: string) =>
        `/automacoes/envios-automatizados/${automationId}`,
      createAutomationByType: (automationTypeId: string) =>
        `/automacoes/envios-automatizados/por-tipo/${automationTypeId}/novo`,
      editAutomationByType: (automationTypeId: string, automationId: string) =>
        `/automacoes/envios-automatizados/por-tipo/${automationTypeId}/editar/${automationId}`,
    },
  },
  debug: {
    myAppModulesPermissionsDebugPage: () => '/debug/my-app-modules-permissions',
    simulateMessageReceiveDebugPage: () => '/debug/simulate-message-receive',
    impersonateDebugPage: () => '/debug/impersonate',
  },
  settings: {
    general: () => '/configuracoes/geral',
    support: () => '/configuracoes/atendimento',
    customColumns: () => '/configuracoes/colunas-customizadas',
    access: () => '/configuracoes/acessos',
    integrationSettings: <Record<string, () => string>>{
      index: () => '/configuracoes/integracoes',
      bling: () => '/configuracoes/integracoes/bling',
      vtex: () => '/configuracoes/integracoes/vtex',
      shopify: () => '/configuracoes/integracoes/shopify',
      lojaIntegrada: () => '/configuracoes/integracoes/loja-integrada',
      wooCommerce: () => '/configuracoes/integracoes/woo-commerce',
      cartPanda: () => '/configuracoes/integracoes/cart-panda',
      omny: () => '/configuracoes/integracoes/omny',
      omie: () => '/configuracoes/integracoes/omie',
      tray: () => '/configuracoes/integracoes/tray',
      unbox: () => '/configuracoes/integracoes/unbox',
      visualECommerce: () => '/configuracoes/integracoes/visual-ecommerce',
      magazord: () => '/configuracoes/integracoes/magazord',
      magento: () => '/configuracoes/integracoes/magento',
      yampi: () => '/configuracoes/integracoes/yampi',
      linxCommerce: () => '/configuracoes/integracoes/linx-commerce',
      nuvemShop: () => '/configuracoes/integracoes/nuvem-shop',
      shoppub: () => '/configuracoes/integracoes/shoppub',
      tiny: () => '/configuracoes/integracoes/tiny',
      vnda: () => '/configuracoes/integracoes/vnda',
      millennium: () => '/configuracoes/integracoes/millennium',
      googleTagManager: () => '/configuracoes/integracoes/google-tag-manager',
      varejoOnline: () => '/configuracoes/integracoes/varejo-online',
    },
    tags: () => '/configuracoes/tags',
    email: () => '/configuracoes/email',
    quickReplies: () => '/configuracoes/respostas-rapidas',
    businessHours: () => '/configuracoes/horario-de-atendimento',
    rolesManagement: {
      index: () => '/configuracoes/cargos',
      edit: (roleId: string) => `/configuracoes/cargos/${roleId}/editar`,
      create: () => '/configuracoes/cargos/novo',
    },
    conversationSectorsManagement: {
      index: () => '/configuracoes/setores-de-conversa',
      create: () => '/configuracoes/setores-de-conversa/novo',
      edit: (conversationSectorId: string) =>
        `/configuracoes/setores-de-conversa/${conversationSectorId}/editar`,
    },
    invoices: () => '/configuracoes/faturas',
  },
  customers: {
    index: () => '/clientes',
  },
  messageTemplates: {
    index: () => '/templates',
    whatsapp: {
      createMessageTemplate: () => '/templates/whatsapp/novo',
      cloneMessageTemplate: (baseTemplateId: string) =>
        `/templates/whatsapp/novo?baseTemplateId=${baseTemplateId}`,
    },
    sms: {
      createMessageTemplate: () => '/templates/sms/novo',
      cloneMessageTemplate: (baseTemplateId: string) =>
        `/templates/sms/novo?baseTemplateId=${baseTemplateId}`,
    },
    email: {
      index: () => '/templates/email',
      createEmailTemplate: () => '/templates/email/novo',
      editEmailTemplate: (emailTemplateId: string) =>
        `/templates/email/${emailTemplateId}/editar`,
    },
  },
  campaigns: {
    create: () => '/campanhas/novo',
    whatsapp: {
      index: () => '/campanhas/whatsapp',
      details: (campaignId: string) => `/campanhas/whatsapp/${campaignId}`,
    },
    email: {
      index: () => '/campanhas/email',
      details: (emailCampaignId: string) =>
        `/campanhas/email/${emailCampaignId}`,
    },
    sms: {
      index: () => '/campanhas/sms',
      details: (campaignId: string) => `/campanhas/sms/${campaignId}`,
    },
  },
  reports: {
    index: () => '/reports/',
  },
  emailOptOut: {
    edit: (customerId: string) => `/cancelar-inscricao/${customerId}/`,
  },
};
