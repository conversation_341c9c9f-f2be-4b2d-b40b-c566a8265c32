import axios from 'axios';
import { AuthService } from '../services/auth.service';
import { showToast } from '../state/globalSlice';
import { store } from '../state/store';
import { baseURL } from './base-url';
import { baseIntegratorURL } from './integrator-base-url';
import { protocol } from './protocol';

// TODO: ADD ABSOLUTEURL TO ENV VAR

const getRequest = ({
  useIntegratorBackendUrl = false,
  informedBaseURL = baseURL,
} = {}) => {
  const axiosInstance = axios.create({
    baseURL: `${protocol}://${(useIntegratorBackendUrl ? baseIntegratorURL : informedBaseURL)}`,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  axiosInstance.interceptors.request.use(function (config) {
    const accessToken = AuthService.getAccessToken();
    config.headers.Authorization = accessToken ? `Bearer ${accessToken}` : '';
    return config;
  });

  axiosInstance.interceptors.response.use(
    (res) => {
      return res;
    },
    (err) => {
      if (
        [401].includes(err?.response?.status) &&
        !err?.response?.data?.message?.includes('Usuário')
      ) {
        AuthService.logout();
        if (!window.location.pathname.includes('cancelar-inscricao')) {
          store.dispatch(
            showToast({
              title: 'Sessão expirada',
              status: 'error',
              duration: 3000,
              isClosable: true,
            }),
          );
        }
      } else if (err?.response?.status === 400) {
        store.dispatch(
          showToast({
            title: err.response.data.message,
            status: 'error',
            duration: 3000,
            isClosable: true,
          }),
        );
      } else if ([504, 0].includes(err?.response?.status)) {
        return;
      } else {
        store.dispatch(
          showToast({
            title:
              'Houve um erro na aplicação, tente novamente mais tarde ou contate o suporte',
            status: 'error',
            duration: 3000,
            isClosable: true,
          }),
        );
      }
      return Promise.reject(err);
    },
  );
  return axiosInstance;
};

export const request = getRequest();
export const integratorRequest = getRequest({
  useIntegratorBackendUrl: true,
});
export const agentRequest = getRequest({
  informedBaseURL: process.env.REACT_APP_AGENT_BASE_URL,
});
