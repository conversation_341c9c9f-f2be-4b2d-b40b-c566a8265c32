import { useMediaQuery } from '@chakra-ui/react';
import { ReactNode, useEffect } from 'react';
import { Navigate, Route, Routes, useLocation } from 'react-router-dom';
import { ReactFlowProvider } from 'reactflow';
import { appPaths } from '../constants/app-paths';
import {
  MAX_MOBILE_SCREEN_WIDTH,
  screenSizes,
} from '../constants/screen-sizes';
import { CrudAutoReplyModalProvider } from '../hooks/useCrudAutoReplyModal';
import { CrudCompanyDefinedFieldModalProvider } from '../hooks/useCrudCompanyDefinedFieldModal';
import { CrudQuickReplyModalProvider } from '../hooks/useCrudQuickReplyModal';
import { CrudTagModalProvider } from '../hooks/useCrudTagModal';
import { SendTemplateModalProvider } from '../hooks/useSendTemplateModal';
import AppLayout from '../layouts/AppLayout';
import AutomationsLayout from '../layouts/AutomationsLayout';
import IntegrationSettingsLayout from '../layouts/IntegrationSettingsLayout';
import SettingsLayout from '../layouts/SettingsLayout';
import AutoRepliesPage from '../pages/AutomationsPage/AutoRepliesPage';
import BackgroundAutomationsPage from '../pages/AutomationsPage/BackgroundAutomationsPage';
import BackgroundAutomationDetailsPage from '../pages/AutomationsPage/BackgroundAutomationsPage/BackgroundAutomationDetailsPage';
import CreateBackgroundAutomationPage from '../pages/AutomationsPage/BackgroundAutomationsPage/CreateBackgroundAutomationPage';
import EditBackgroundAutomationPage from '../pages/AutomationsPage/BackgroundAutomationsPage/EditBackgroundAutomationPage';
import MessagesFlowPage from '../pages/AutomationsPage/MessageFlowsPage';
import EditMessageFlowPage from '../pages/AutomationsPage/MessageFlowsPage/EditMessageFlowPage';
import CreateCampaignPage from '../pages/CampaignsPage/CreateCampaignPage';
import SmsCampaignsPage from '../pages/CampaignsPage/SmsCampaignsPage';
import WhatsappCampaignsPage from '../pages/CampaignsPage/WhatsappCampaignsPage';
import WhatsappCampaignDetailsPage from '../pages/CampaignsPage/WhatsappCampaignsPage/WhatsappCampaignDetailsPage';
import CustomersPage from '../pages/CustomersPage';
import HomePage from '../pages/HomePage';
import InboxPage from '../pages/InboxPage';
import ReportsOverviewPage from '../pages/ReportsPage/OverviewPage';
import ReportsLayout from '../pages/ReportsPage/ReportsLayout';
import ColumnSettingsPage from '../pages/SettingsPage/ColumnSettingsPage';
import IntegrationSettingsPage from '../pages/SettingsPage/IntegrationSettingsPage';
import BlingIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/BlingIntegrationPage';
import LojaIntegradaIntegrationsPage from '../pages/SettingsPage/IntegrationSettingsPage/LojaIntegradaIntegrationsPage';
import ShopifyIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/ShopifyIntegrationPage';
import VtexIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/VtexIntegrationPage';
import CartPandaIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/CartPandaIntegrationPage';
import InvoicesPage from '../pages/SettingsPage/InvoicesPage';
import TagSettingsPage from '../pages/SettingsPage/TagSettingsPage';
import { MixpanelService } from '../services/mixpanel.service';
import OmnyIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/OmnyIntegrationPage';
import TrayIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/TrayIntegrationPage';
import OmieIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/OmieIntegrationPage';
import MagentoIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/MagentoIntegrationPage';
import MagazordIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/MagazordIntegrationPage';
import VisualECommerceIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/VisualECommerceIntegrationPage';
import UnboxIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/UnboxIntegrationPage';
import EditBackgroundAutomationByTypePage from '../pages/AutomationsPage/BackgroundAutomationsPage/EditBackgroundAutomationByTypePage';
import CreateBackgroundAutomationByTypePage from '../pages/AutomationsPage/BackgroundAutomationsPage/CreateBackgroundAutomationByTypePage';
import LinxCommerceIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/LinxCommerceIntegrationPage';
import NuvemShopIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/NuvemShopIntegrationPage';
import TemplatesPage from '../pages/TemplatePage';
import CreateEmailTemplatePage from '../pages/TemplatePage/EmailTemplate/CreateEmailTemplatePage';
import EditEmailTemplatePage from '../pages/TemplatePage/EmailTemplate/EditEmailTemplatePage';
import EmailSettingsPage from '../pages/SettingsPage/EmailSettingsPage';
import EmailCampaignsPage from '../pages/CampaignsPage/EmailCampaignsPage';
import EmailCampaignDetailsPage from '../pages/CampaignsPage/EmailCampaignsPage/EmailCampaignDetailsPage';
import { EmailOptOutPage } from '../pages/EmailOptOutPage';
import ShoppubIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/ShoppubPage';
import TinyIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/TinyIntegrationPage';
import DebugLayout from '../layouts/DebugLayout';
import ReceiveMessageDebugPage from '../pages/DebugPage/ReceiveMessageDebugPage';
import VndaIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/VndaIntegrationPage';
import MillenniumIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/MillenniumIntegrationPage';
import { AppModule, AppModulesEnum } from '../types/AppModule';
import { useAppModuleAccessGuard } from '../hooks/useAppModuleAccessGuard';
import MyAppModulesPermissions from '../pages/DebugPage/MyAppModulesPermissionsDebugPage';
import ImpersonateDebugPage from '../pages/DebugPage/ImpersonateDebugPage';
import BackgroundCashbackPage from '../pages/AutomationsPage/BackgroundCashbackPage';
import GoogleTagManagerIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/GoogleTagManagerIntegrationPage';
import { SIDEBAR_OPTIONS } from '../components/Sidebar';
import CreateMessageTemplatePage from '../pages/TemplatePage/components/CreateMessageTemplatePage';
import { CommunicationChannelEnum } from '../types/CommunicationChannelEnum';
import BackgroundSettingsPage from '../pages/SettingsPage/BackgroundSettingsPage';
import BackgroundSupportPage from '../pages/SettingsPage/BackgroundSupportPage';
import BackgroundAccessPage from '../pages/SettingsPage/BackgroundAccessPage';
import { CrudRoleProvider } from '../hooks/useCrudRoleModal';
import { CrudSectorModalProvider } from '../hooks/useCrudConversationSectorModal';
import VarejoOnlineIntegrationPage from '../pages/SettingsPage/IntegrationSettingsPage/VarejoOnlineIntegrationPage';

const ConversationsElement = () => (
  <SendTemplateModalProvider>
    <InboxPage />
  </SendTemplateModalProvider>
);

export interface AppRoute {
  path: string;
  element: ReactNode;
  appModule: AppModule[] | null;
}

const routes: AppRoute[] = [
  {
    path: appPaths.conversations(),
    element: <ConversationsElement />,
    appModule: [AppModulesEnum.CHAT],
  },
  {
    path: appPaths.home(),
    element: <HomePage />,
    appModule: [AppModulesEnum.HOME],
  },
  {
    path: appPaths.emailOptOut.edit(':emailOptOutId'),
    element: <EmailOptOutPage />,
    appModule: null,
  },
  {
    path: appPaths.automations.messageFlows.editMessageFlow(':flowId'),
    element: (
      <ReactFlowProvider>
        <EditMessageFlowPage />
      </ReactFlowProvider>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.messageFlows.copyAndEditMessageFlow(':flowId'),
    element: (
      <ReactFlowProvider>
        <EditMessageFlowPage />
      </ReactFlowProvider>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.messageFlows.index(),
    element: (
      <AutomationsLayout>
        <MessagesFlowPage />
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.cashback(),
    element: (
      <AutomationsLayout>
        <BackgroundCashbackPage />
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.autoReplies(),
    element: (
      <AutomationsLayout>
        <CrudAutoReplyModalProvider>
          <AutoRepliesPage />
        </CrudAutoReplyModalProvider>
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.backgroundAutomations.index(),
    element: (
      <AutomationsLayout>
        <CrudAutoReplyModalProvider>
          <BackgroundAutomationsPage />
        </CrudAutoReplyModalProvider>
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.backgroundAutomations.createAutomation(),
    element: (
      <AutomationsLayout>
        <CrudAutoReplyModalProvider>
          <CreateBackgroundAutomationPage />
        </CrudAutoReplyModalProvider>
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.backgroundAutomations.editAutomation(
      ':automationId',
    ),
    element: (
      <AutomationsLayout>
        <CrudAutoReplyModalProvider>
          <EditBackgroundAutomationPage />
        </CrudAutoReplyModalProvider>
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.backgroundAutomations.details(':automationId'),
    element: <BackgroundAutomationDetailsPage />,
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.backgroundAutomations.createAutomationByType(
      ':automationTypeId',
    ),
    element: (
      <AutomationsLayout>
        <CrudAutoReplyModalProvider>
          <CreateBackgroundAutomationByTypePage />
        </CrudAutoReplyModalProvider>
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.automations.backgroundAutomations.editAutomationByType(
      ':automationTypeId',
      ':automationId',
    ),
    element: (
      <AutomationsLayout>
        <CrudAutoReplyModalProvider>
          <EditBackgroundAutomationByTypePage />
        </CrudAutoReplyModalProvider>
      </AutomationsLayout>
    ),
    appModule: [AppModulesEnum.AUTOMATIONS],
  },
  {
    path: appPaths.debug.myAppModulesPermissionsDebugPage(),
    element: (
      <DebugLayout>
        <MyAppModulesPermissions />
      </DebugLayout>
    ),
    appModule: [AppModulesEnum.DEBUG_TOOLS],
  },
  {
    path: appPaths.debug.simulateMessageReceiveDebugPage(),
    element: (
      <DebugLayout>
        <ReceiveMessageDebugPage />
      </DebugLayout>
    ),
    appModule: [AppModulesEnum.DEBUG_TOOLS],
  },
  {
    path: appPaths.debug.impersonateDebugPage(),
    element: (
      <DebugLayout>
        <ImpersonateDebugPage />
      </DebugLayout>
    ),
    appModule: [AppModulesEnum.DEBUG_TOOLS],
  },
  {
    path: appPaths.settings.general(),
    element: (
      <SettingsLayout>
        <BackgroundSettingsPage />
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.support(),
    element: (
      <SettingsLayout>
        <CrudQuickReplyModalProvider>
          <BackgroundSupportPage />
        </CrudQuickReplyModalProvider>
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.access(),
    element: (
      <SettingsLayout>
        <CrudRoleProvider>
          <CrudSectorModalProvider>
            <BackgroundAccessPage />
          </CrudSectorModalProvider>
        </CrudRoleProvider>
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.customColumns(),
    element: (
      <SettingsLayout>
        <CrudCompanyDefinedFieldModalProvider>
          <ColumnSettingsPage />
        </CrudCompanyDefinedFieldModalProvider>
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.tags(),
    element: (
      <SettingsLayout>
        <CrudTagModalProvider>
          <TagSettingsPage />
        </CrudTagModalProvider>
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.email(),
    element: (
      <SettingsLayout>
        <EmailSettingsPage />
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.shopify(),
    element: (
      <IntegrationSettingsLayout title="Shopify">
        <ShopifyIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.lojaIntegrada(),
    element: (
      <IntegrationSettingsLayout title="Loja Integrada">
        <LojaIntegradaIntegrationsPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.bling(),
    element: (
      <IntegrationSettingsLayout title="Bling">
        <BlingIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.vtex(),
    element: (
      <IntegrationSettingsLayout title="VTEX">
        <VtexIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.cartPanda(),
    element: (
      <IntegrationSettingsLayout title="Cart Panda">
        <CartPandaIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.unbox(),
    element: (
      <IntegrationSettingsLayout title="Unbox">
        <UnboxIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.visualECommerce(),
    element: (
      <IntegrationSettingsLayout title="Visual E-Commerce">
        <VisualECommerceIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.magazord(),
    element: (
      <IntegrationSettingsLayout title="Magazord">
        <MagazordIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.magento(),
    element: (
      <IntegrationSettingsLayout title="Magento">
        <MagentoIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.linxCommerce(),
    element: (
      <IntegrationSettingsLayout title="Linx Commerce">
        <LinxCommerceIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.omny(),
    element: (
      <IntegrationSettingsLayout title="Omny">
        <OmnyIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.omie(),
    element: (
      <IntegrationSettingsLayout title="Omie">
        <OmieIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.tray(),
    element: (
      <IntegrationSettingsLayout title="Tray">
        <TrayIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.nuvemShop(),
    element: (
      <IntegrationSettingsLayout title="Nuvem Shop">
        <NuvemShopIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.shoppub(),
    element: (
      <IntegrationSettingsLayout title="Shoppub">
        <ShoppubIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.tiny(),
    element: (
      <IntegrationSettingsLayout title="Tiny">
        <TinyIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.vnda(),
    element: (
      <IntegrationSettingsLayout title="Vnda">
        <VndaIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.millennium(),
    element: (
      <IntegrationSettingsLayout title="Millennium">
        <MillenniumIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.varejoOnline(),
    element: (
      <IntegrationSettingsLayout title="Varejo Online">
        <VarejoOnlineIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.googleTagManager(),
    element: (
      <IntegrationSettingsLayout title="Google Tag Manager">
        <GoogleTagManagerIntegrationPage />
      </IntegrationSettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.integrationSettings.index(),
    element: (
      <SettingsLayout>
        <IntegrationSettingsPage />
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.settings.invoices(),
    element: (
      <SettingsLayout>
        <InvoicesPage />
      </SettingsLayout>
    ),
    appModule: [AppModulesEnum.SETTINGS],
  },
  {
    path: appPaths.customers.index(),
    element: (
      <SendTemplateModalProvider>
        <CustomersPage />
      </SendTemplateModalProvider>
    ),
    appModule: [AppModulesEnum.CUSTOMERS, AppModulesEnum.CAMPAIGNS],
  },
  {
    path: appPaths.messageTemplates.index(),
    element: <TemplatesPage />,
    appModule: [AppModulesEnum.TEMPLATES],
  },
  {
    path: appPaths.messageTemplates.whatsapp.createMessageTemplate(),
    element: (
      <CreateMessageTemplatePage
        communicationChannel={CommunicationChannelEnum.WHATSAPP}
      />
    ),
    appModule: [AppModulesEnum.TEMPLATES],
  },
  {
    path: appPaths.messageTemplates.sms.createMessageTemplate(),
    element: (
      <CreateMessageTemplatePage
        communicationChannel={CommunicationChannelEnum.SMS}
      />
    ),
    appModule: [AppModulesEnum.TEMPLATES],
  },
  {
    path: appPaths.messageTemplates.email.index(),
    element: <TemplatesPage tab={'email'} />,
    appModule: [AppModulesEnum.TEMPLATES],
  },
  {
    path: appPaths.messageTemplates.email.createEmailTemplate(),
    element: <CreateEmailTemplatePage />,
    appModule: [AppModulesEnum.TEMPLATES],
  },
  {
    path: appPaths.messageTemplates.email.editEmailTemplate(':emailTemplateId'),
    element: <EditEmailTemplatePage />,
    appModule: [AppModulesEnum.TEMPLATES],
  },
  {
    path: appPaths.campaigns.create(),
    element: <CreateCampaignPage />,
    appModule: [AppModulesEnum.CAMPAIGNS],
  },
  {
    path: appPaths.campaigns.sms.index(),
    element: <SmsCampaignsPage />,
    appModule: [AppModulesEnum.CAMPAIGNS],
  },
  {
    path: appPaths.campaigns.whatsapp.index(),
    element: <WhatsappCampaignsPage />,
    appModule: [AppModulesEnum.CAMPAIGNS],
  },
  {
    path: appPaths.campaigns.email.index(),
    element: <EmailCampaignsPage />,
    appModule: [AppModulesEnum.CAMPAIGNS],
  },
  {
    path: appPaths.campaigns.whatsapp.details(':campaignId'),
    element: <WhatsappCampaignDetailsPage />,
    appModule: [AppModulesEnum.CAMPAIGNS],
  },
  {
    path: appPaths.campaigns.email.details(':emailCampaignId'),
    element: <EmailCampaignDetailsPage />,
    appModule: [AppModulesEnum.CAMPAIGNS],
  },
  {
    path: appPaths.reports.index(),
    element: (
      <ReportsLayout>
        <ReportsOverviewPage />
      </ReportsLayout>
    ),
    appModule: [AppModulesEnum.CAMPAIGNS],
  },
];

export const appRoutes = routes;

const AppRoutes = () => {
  const location = useLocation();
  const [isMobile] = useMediaQuery(screenSizes.mobile);
  const { checkUserHasAppModuleAccess } = useAppModuleAccessGuard();

  const getFirstAccessibleIndexRoute = () => {
    return (
      routes.find(
        (route) =>
          SIDEBAR_OPTIONS.some((option) => option.path === route.path) &&
          checkUserHasAppModuleAccess(route.appModule),
      )?.path || appPaths.home()
    );
  };

  useEffect(() => {
    MixpanelService.track('page-view', { path: location.pathname });
  }, [location.pathname]);

  if (window.screen.width < MAX_MOBILE_SCREEN_WIDTH) {
    return <ConversationsElement />;
  }

  return (
    <Routes>
      {routes.map(
        ({ path, element, appModule }) =>
          checkUserHasAppModuleAccess(appModule) && (
            <Route
              key={path}
              path={path}
              element={<AppLayout>{element}</AppLayout>}
            />
          ),
      )}
      <Route
        path={'/*'}
        element={
          <Navigate
            to={
              isMobile
                ? appPaths.conversations()
                : getFirstAccessibleIndexRoute()
            }
            replace
          />
        }
      />
    </Routes>
  );
};

export default AppRoutes;
