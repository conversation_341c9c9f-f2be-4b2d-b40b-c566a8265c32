import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { FormDataUtils } from '../utils/form-data.utils';

export interface UploadOrdersDto extends Record<any, any> {}

const uploadOrders = async (uploadOrdersDto: UploadOrdersDto) => {
  const { file } = uploadOrdersDto;
  const data = FormDataUtils.convertJsonToFormData({
    file,
  });

  return request.post(apiRoutes.uploadOrders(), data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 60000,
  });
};

interface ListCouponsResponse {
  coupons: string[];
}

const listOrdersCoupons = async (): Promise<
  AxiosResponse<ListCouponsResponse>
> => {
  return request.get(apiRoutes.listOrdersCoupons());
};

const listOrdersStatuses = async (): Promise<
  AxiosResponse<ListCouponsResponse>
> => {
  return request.get(apiRoutes.listOrdersStatuses());
};

export const OrdersService = {
  uploadOrders,
  listOrdersCoupons,
  listOrdersStatuses,
};
