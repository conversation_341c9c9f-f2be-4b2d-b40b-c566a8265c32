import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';

export interface SendEmailTemplateTestDto {
  emailTemplateId: string;
  recipientName: string;
  recipientEmail: string;
  templateArgs: Record<string, string | undefined | null>;
}

const sendEmailTemplateTest = async (
  sendEmailTemplateTestDto: SendEmailTemplateTestDto,
) => {
  return request.post(
    apiRoutes.sendEmailTemplateTest(),
    sendEmailTemplateTestDto,
  );
};

export const EmailService = {
  sendEmailTemplateTest,
};
