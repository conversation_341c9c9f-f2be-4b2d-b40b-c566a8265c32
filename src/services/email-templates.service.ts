import { AxiosResponse } from 'axios';
import { apiRoutes } from '../constants/api-routes';
import { request } from '../constants/request';
import { EmailTemplate } from '../types/Prisma';

export interface CreateEmailTemplateDto {
  name: string;
  subject: string;
  category: string;
  html: string;
  text: string;
  unlayerDesign: JSON;
}

const listEmailTemplates = async (): Promise<
  AxiosResponse<EmailTemplate[]>
> => {
  return request.get(apiRoutes.listEmailTemplates());
};

const deleteEmailTemplate = async (emailTemplateId: string): Promise<void> => {
  return request.delete(apiRoutes.deleteEmailTemplate(emailTemplateId));
};

const createEmailTemplate = async (
  createEmailTemplateDto: CreateEmailTemplateDto,
) => {
  return request.post(apiRoutes.createEmailTemplate(), createEmailTemplateDto, {
    timeout: 30000,
  });
};

const validateEmailTemplate = async (
  createEmailTemplateDto: Pick<CreateEmailTemplateDto, 'html' | 'category'>,
) => {
  return request.post(
    apiRoutes.validateEmailTemplate(),
    createEmailTemplateDto,
    {
      timeout: 30000,
    },
  );
};

const updateEmailTemplate = async (
  emailTemplateId: string,
  createEmailTemplateDto: CreateEmailTemplateDto,
) => {
  return request.put(
    apiRoutes.updateEmailTemplate(emailTemplateId),
    createEmailTemplateDto,
    {
      timeout: 30000,
    },
  );
};

export const EmailTemplatesService = {
  listEmailTemplates,
  deleteEmailTemplate,
  createEmailTemplate,
  updateEmailTemplate,
  validateEmailTemplate,
};
