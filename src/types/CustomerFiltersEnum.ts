export enum CustomerFiltersEnum {
  SEARCH_QUERY = 'searchQuery',
  SELECTED_ENGAGEMENT_ACTION_TYPES = 'selectedEngagementActionTypes',
  SELECTED_ENGAGEMENT_EMAIL_ACTION_TYPES = 'selectedEmailEngagementActionTypes',
  MAX_TOTAL_PURCHASES = 'maxTotalPurchases',
  MIN_TOTAL_PURCHASES = 'minTotalPurchases',
  MIN_TOTAL_ORDERS = 'minTotalOrders',
  MAX_TOTAL_ORDERS = 'maxTotalOrders',
  SELECTED_ENGAGEMENT_TEMPLATE_IDS = 'selectedEngagementTemplateIds',
  SELECTED_ENGAGEMENT_EMAIL_TEMPLATE_IDS = 'selectedEngagementEmailTemplateIds',
  MIN_AVERAGE_ORDER_VALUE = 'minAverageOrderValue',
  MAX_AVERAGE_ORDER_VALUE = 'maxAverageOrderValue',
  START_ORDERS_CREATED_AT = 'startOrdersCreatedAt',
  END_ORDERS_CREATED_AT = 'endOrdersCreatedAt',
  MIN_DAYS_SINCE_LAST_CAMPAIGN = 'minDaysSinceLastCampaign',
  MIN_DAYS_SINCE_LAST_EMAIL_CAMPAIGN = 'minDaysSinceLastEmailCampaign',
  MIN_AVERAGE_ITEM_VALUE = 'minAverageItemValue',
  MAX_AVERAGE_ITEM_VALUE = 'maxAverageItemValue',
  SELECTED_TAGS = 'selectedTags',
  MIN_DAYS_SINCE_LAST_PURCHASE = 'minDaysSinceLastPurchase',
  MAX_DAYS_SINCE_LAST_PURCHASE = 'maxDaysSinceLastPurchase',
  EXACT_DAYS_SINCE_LAST_PURCHASE = 'exactDaysSinceLastPurchase',
  EXCLUDED_TAGS = 'excludedTags',
  SELECTED_DEFAULT_AGENT_IDS = 'selectedDefaultAgentIds',
  IS_REMARKETING = 'isRemarketing',
  EXCLUDED_TEMPLATE_IDS = 'excludedTemplateIds',
  EXCLUDED_EMAIL_TEMPLATE_IDS = 'excludedEmailTemplateIds',
  SELECTED_PRODUCT_IDS = 'selectedProductIds',
  SELECTED_PRODUCT_COMPARATOR = 'selectedProductComparator',
  EXCLUDED_PRODUCT_COMPARATOR = 'excludedProductComparator',
  EXCLUDED_PRODUCT_IDS = 'excludedProductIds',
  MIN_PRODUCT_QUANTITY = 'minProductQuantity',
  MAX_PRODUCT_QUANTITY = 'maxProductQuantity',
  MIN_DAYS_SINCE_LAST_PRODUCT_PURCHASE = 'minDaysSinceLastProductPurchase',
  MAX_DAYS_SINCE_LAST_PRODUCT_PURCHASE = 'maxDaysSinceLastProductPurchase',
  PRODUCT_NAME_CONTAINS = 'productNameContains',
  IS_LAST_PRODUCT_PURCHASED = 'isLastProductPurchased',
  SELECTED_STATES = 'selectedStates',
  SELECTED_COUPONS = 'selectedCoupons',
  SELECTED_ORDERS_STATUSES = 'selectedOrdersStatuses',
  SELECTED_CITIES = 'selectedCities',
  HAS_EMAIL = 'hasEmail',
  SELECTED_CAMPAIGN_CHANNEL = 'selectedCampaignChannel',
  DAYS_UNTIL_BIRTHDAY = 'daysUntilBirthday',

  CUSTOM_FIELD_ID_1 = 'customFieldId1',
  CUSTOM_FIELD_VALUE_1 = 'customFieldValue1',
  CUSTOM_FIELD_COMPARISON_TYPE_1 = 'customFieldComparisonType1',

  // hidden filters
  IS_SCHEDULED_CAMPAIGNS_VISIBLE = 'isScheduledCampaignsVisible',
  IS_SCHEDULED_EMAIL_CAMPAIGNS_VISIBLE = 'isScheduledEmailCampaignsVisible',
  RFM_GROUP = 'rfmGroup',
  PLATFORM_ORDER_SOURCE = 'platformOrderSource',

  // extra options
  SORT_BY = 'sortBy',
}
