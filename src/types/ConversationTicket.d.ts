import { ConversationWithIncludes } from './Conversation';
import Prisma from './Prisma';

export type ConversationTicketStatus = 'open' | 'closed';

export interface ConversationTicket extends Prisma.ConversationTicket {
  createdAt: string;
  updatedAt: string;
  finishedAt: string | null;
  firstResponseAt: string | null;
  agentId: string | null;
}

export interface ConversationTicketWithIncludes extends ConversationTicket {
  conversation: ConversationWithIncludes;
}
