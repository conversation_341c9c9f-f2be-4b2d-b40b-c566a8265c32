import { Node } from 'reactflow';
import { FlowTriggerType } from './Prisma';

export type NodeType =
  | 'send_whatsapp_message'
  | 'trigger'
  | 'move_conversation_to_category'
  | 'send_whatsapp_media'
  | 'add_tag_to_customer'
  | 'end_whatsapp_conversation'
  | 'save_customer_response'
  | 'conditions_check'
  | 'http_request'
  | 'time_delay'
  | 'send_whatsapp_message_template';

export type FlowNodeButton = {
  id: string;
  type: any;
  text: string;
  url?: string;
};

export type FlowNodeCondition = {
  id: string;
  type:
    | 'total_orders'
    | 'total_purchases'
    | 'days_since_last_purchase'
    | 'has_tag'
    | 'abandoned_cart_value'
    | 'default';
  value: Prisma.JsonValue;
  comparisonOperator: 'GREATER_THAN' | 'LESS_THAN' | 'EQUALS';
  flowNodeConditionBlockId: string;
};

export interface SendWhatsappMessageNodeData {
  text?: string;
  buttons?: FlowNodeButton[];
}

export interface SendWhatsappMediaNodeData {
  file?: File;
  fileId?: string;
  fileKey?: string;
  fileName?: string;
  mediaType?: 'image' | 'video' | 'audio' | 'file';
}

export interface TriggerNodeData {
  flowTriggers: {
    id: string;
    type: FlowTriggerType;
    text: string;
    isDefault: boolean;
    messageTemplateButtonId?: string;
  }[];
}

export interface MoveConversationToCategoryNodeData {
  targetConversationCategoryId: string;
}
export interface AddTagToCustomerNodeData {
  tagId: string;
}

export interface SaveCustomerResponseNodeData {
  companyDefinedFieldId: string;
}

export interface TimeDelayNodeData {
  unit: 'minutes' | 'hours';
  quantity: number;
}

export interface FlowNodeConditionBlock {
  id: string;
  flowNodeConditions: FlowNodeCondition[];
  conditionalJoinType: 'AND' | 'OR';
  priority: number;
  targetFlowNodeId: string;
}

export interface ConditionsCheckNodeData {
  flowNodeConditionBlocks: FlowNodeConditionBlock[];
}

export interface SendWhatsappMessageTemplateNodeData {
  messageTemplateId: string;
  templateArgs?: Record<string, string>;
}

export interface HttpRequestNodeData {
  url: string;
  method: string;
  headers: KeyValuePair[];
  body: string;
  dynamicVariables: KeyValuePair[];
}

export type NodeData =
  | SendWhatsappMessageNodeData
  | TriggerNodeData
  | MoveConversationToCategoryNodeData
  | SendWhatsappMediaNodeData
  | AddTagToCustomerNodeData
  | EndWhatsappConversationNodeData
  | SaveCustomerResponseNodeData
  | ConditionsCheckNodeDate
  | HttpRequestNodeData;

export interface ReactFlowNode extends Node<any, NodeType> {
  data: NodeType extends 'send_whatsapp_message'
    ? SendWhatsappMessageNodeData
    : NodeType extends 'trigger'
      ? TriggerNodeData
      : NodeType extends 'move_conversation_to_category'
        ? MoveConversationToCategoryNodeData
        : NodeType extends 'send_whatsapp_media'
          ? SendWhatsappMediaNodeData
          : NodeType extends AddTagToCustomerNodeData
            ? AddTagToCustomerNodeData
            : NodeType extends EndWhatsappConversationNodeData
              ? EndWhatsappConversationNodeData
              : NodeType extends SaveCustomerResponseNodeData
                ? SaveCustomerResponseNodeData
                : NodeType extends ConditionsCheckNodeData
                  ? ConditionsCheckNodeData
                  : NodeType extends TimeDelayNodeData
                    ? TimeDelayNodeData
                    : NodeType extends SendWhatsappMessageTemplateNodeData
                      ? SendWhatsappMessageTemplateNodeData
                      : NodeType extends HttpRequestNodeData
                        ? HttpRequestNodeData
                        : any;
}

export interface EndWhatsappConversationNodeData {}
