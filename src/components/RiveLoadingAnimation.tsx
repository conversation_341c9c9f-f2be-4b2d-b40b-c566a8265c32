import React, { useState, useEffect } from 'react';
import { Flex } from '@chakra-ui/react';
import Rive from '@rive-app/react-canvas';

interface RiveLoadingAnimationProps {
  /** Whether the loading animation is active */
  isLoading: boolean;
  /** Whether there are results/data available */
  hasResults: boolean;
  /** Height of the animation container */
  height?: string | number;
  /** Width of the animation container */
  width?: string | number;
  /** Path to the Rive animation file */
  animationFile?: string;
  /** Callback when loading animation completes */
  onLoadingComplete?: () => void;
  /** Duration to show the success animation before calling onLoadingComplete (in ms) */
  successAnimationDuration?: number;
}

const RiveLoadingAnimation = ({
  isLoading,
  hasResults,
  height = '100px',
  width = '100%',
  animationFile = '/animations/rive/ai-generate-loading.riv',
  onLoadingComplete,
  successAnimationDuration = 1450,
}: RiveLoadingAnimationProps) => {
  const [showLoading, setShowLoading] = useState(true);
  const [currentArtBoard, setCurrentArtBoard] = useState('Magic');

  useEffect(() => {
    if (hasResults && !isLoading) {
      // Switch to success animation
      setCurrentArtBoard('Tick blue');
      
      // Hide loading after success animation duration
      const hideTimeout = setTimeout(() => {
        setShowLoading(false);
        onLoadingComplete?.();
      }, successAnimationDuration);

      return () => clearTimeout(hideTimeout);
    } else if (isLoading) {
      // Reset to loading state
      setShowLoading(true);
      setCurrentArtBoard('Magic');
    }
  }, [isLoading, hasResults, onLoadingComplete, successAnimationDuration]);

  // Don't render if not loading and no results
  if (!isLoading && !hasResults) {
    return null;
  }

  // Don't render if loading is complete and we should hide
  if (!showLoading) {
    return null;
  }

  return (
    <Flex height={height} width={width} align="center" justify="center">
      <Rive
        key={currentArtBoard}
        src={animationFile}
        artboard={currentArtBoard}
        stateMachines="State appear"
      />
    </Flex>
  );
};

export default RiveLoadingAnimation;
