import { TriangleDownIcon, TriangleUpIcon } from '@chakra-ui/icons';
import {
  Box,
  chakra,
  Flex,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from '@chakra-ui/react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import React, { useEffect, useRef, useState } from 'react';
import { useQuery } from 'react-query';
import { useVirtual } from 'react-virtual';
import { colors } from '../../constants/colors';
import { request } from '../../constants/request';
import { PaginatedResponse } from '../../types/PaginatedResponse';
import { TableHeaders } from '../../types/TableHeaders';
import { UrlUtils } from '../../utils/url.utils';
import IndeterminateCheckbox from '../IndeterminateCheckbox';
import LoadingScreen from '../LoadingScreen';
import Pagination from '../Pagination';
import { scrollbarStyles } from '../../styles/scrollbar.styles';

const getRowId = (row: any, relativeIndex: any, parent: any) => {
  return parent ? [parent.id, row.uniqueId].join('.') : row.id;
};

interface DataTableServerPaginatedProps {
  headersUrl: string;
  dataUrl: string;
  queryParameters?: Record<string, string | undefined | null>;
  transformHeader?: (column: TableHeaders) => ColumnDef<any>;
  rowSelection?: Record<string, any>;
  onRowSelectionChange?: (selectedRows: Record<string, boolean>) => void;
  showRowSelection?: boolean;
  refetchKey?: number | string;
  rowHeight?: number;
  columnWidths?: Record<string, string>;
}

const DataTableServerPaginated = <DataType extends object>({
  headersUrl,
  dataUrl,
  queryParameters,
  transformHeader,
  rowSelection,
  onRowSelectionChange,
  showRowSelection = true,
  refetchKey = 0,
  rowHeight = 70,
  columnWidths = {},
}: DataTableServerPaginatedProps) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [dataUrlWithParams, setDataUrlWithParams] = useState(
    `${dataUrl}?page=${currentPage}&perPage=${rowsPerPage}&${UrlUtils.convertObjectToQueryString(
      queryParameters,
    )}`,
  );

  const defaultWidth = '200px';

  const { data: tableHeaders, isFetching: isFetchingHeaders } = useQuery<
    TableHeaders[]
  >(headersUrl, async () => {
    const { data } = await request.get(headersUrl);
    return data;
  });

  const {
    data: tableData,
    refetch: refetchTableData,
    isFetching: isFetchingData,
  } = useQuery<PaginatedResponse<DataType>>(dataUrlWithParams, async () => {
    const { data } = await request.get(dataUrlWithParams, {
      timeout: 1000 * 120,
    });
    return data;
  });

  const getTransformedHeaders = () => {
    if (!tableHeaders) return [];
    let headers = tableHeaders
      .filter((header: TableHeaders) => header.isActive)
      .map((header: TableHeaders) => {
        const baseHeader = transformHeader ? transformHeader(header) : header;

        const headerId = header.id || header.accessorKey || '';

        const width = columnWidths[headerId] || defaultWidth;

        return {
          ...baseHeader,
          meta: {
            ...baseHeader.meta,
            width,
          },
        };
      });

    headers = [
      {
        id: 'select',
        header: (info: any) => {
          if (!info) return <div />;
          const table = info.table;
          return (
            <IndeterminateCheckbox
              {...{
                checked: table.getIsAllPageRowsSelected(),
                indeterminate: table.getIsSomeRowsSelected(),
                onChange: table.getToggleAllPageRowsSelectedHandler(),
              }}
            />
          );
        },
        cell: ({ row }: any) => {
          return (
            <IndeterminateCheckbox
              {...{
                checked: row.getIsSelected(),
                disabled: !row.getCanSelect(),
                indeterminate: row.getIsSomeSelected(),
                onChange: row.getToggleSelectedHandler(),
              }}
            />
          );
        },
        meta: {
          width: columnWidths['select'] || '50px',
        },
      },
      ...headers,
    ];

    return headers;
  };

  const { getHeaderGroups, getRowModel } = useReactTable({
    getRowId,
    state: {
      rowSelection,
    },
    columns: getTransformedHeaders() || [],
    data: tableData?.data || [],
    getCoreRowModel: getCoreRowModel(),
    enableRowSelection: true,
    onRowSelectionChange: (updater) => {
      if (onRowSelectionChange) {
        if (typeof updater === 'function') {
          const newRowSelection = updater(rowSelection || {});
          onRowSelectionChange(newRowSelection);
        } else {
          onRowSelectionChange(updater);
        }
      }
    },
  });

  const { rows } = getRowModel();
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const rowVirtualizer = useVirtual({
    parentRef: tableContainerRef,
    size: rows.length,
    overscan: 10,
  });

  const { virtualItems: virtualRows, totalSize } = rowVirtualizer;
  const paddingTop = virtualRows.length > 0 ? virtualRows?.[0]?.start || 0 : 0;
  const paddingBottom =
    virtualRows.length > 0
      ? totalSize - (virtualRows?.[virtualRows.length - 1]?.end || 0)
      : 0;

  useEffect(() => {
    refetchTableData();
  }, [refetchTableData, refetchKey]);

  useEffect(() => {
    if (currentPage === 1) return;
    setDataUrlWithParams(
      `${dataUrl}?page=${currentPage}&perPage=${rowsPerPage}&${UrlUtils.convertObjectToQueryString(
        queryParameters,
      )}`,
    );
  }, [currentPage, rowsPerPage]);

  useEffect(() => {
    setCurrentPage(1);
    setDataUrlWithParams(
      `${dataUrl}?page=${1}&perPage=${rowsPerPage}&${UrlUtils.convertObjectToQueryString(
        queryParameters,
      )}`,
    );
  }, [dataUrl, rowsPerPage, JSON.stringify(queryParameters), refetchTableData]);

  const columnCount = getHeaderGroups()[0]?.headers?.length || 0;

  // Merged getColumnWidth function that combines both approaches
  const getColumnWidth = (columnDef: any, index?: number) => {
    // If we have the columnDef, use its meta.width (from HEAD version)
    if (columnDef?.meta?.width) {
      return columnDef.meta.width as string | undefined;
    }

    // Otherwise use the dynamic calculation (from the other branch)
    if (index !== undefined) {
      if (index === 0) return '60px';

      const minColumnWidth = 160;
      const maxAvailableWidth = window.innerWidth;

      if (columnCount <= 7) {
        const dynamicWidth = Math.floor(maxAvailableWidth / columnCount);
        return `${Math.max(dynamicWidth, minColumnWidth)}px`;
      }

      return `${minColumnWidth}px`;
    }

    return defaultWidth;
  };

  return (
    <LoadingScreen isLoading={isFetchingData || isFetchingHeaders}>
      <Flex direction="column" width="100%">
        <TableContainer
          height={'78vh'}
          ref={tableContainerRef}
          overflowY="scroll"
          overflowX="auto"
          css={scrollbarStyles({ height: '5px' })}
          border="1px solid"
          borderColor="gray.200"
          borderRadius="md"
          borderBottomRadius="0"
          position="relative"
          sx={{
            '& th, & td': {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          }}
        >
          <Table
            width="100%"
            sx={{
              tableLayout: 'fixed',
              borderCollapse: 'separate',
              borderSpacing: 0,
            }}
          >
            <colgroup>
              {getHeaderGroups()[0]?.headers.map((header) => (
                <col
                  key={header.id}
                  style={{ width: getColumnWidth(header.column.columnDef) }}
                />
              ))}
            </colgroup>
            <Thead position="sticky" top={0} bgColor={colors.red} zIndex={1}>
              {getHeaderGroups().map((headerGroup) => (
                <Tr key={headerGroup.id} boxShadow="md">
                  {headerGroup.headers.map((header, index) => (
                    <Th
                      key={header.id}
                      backgroundColor="white"
                      width={getColumnWidth(header.column.columnDef, index)}
                      minWidth={getColumnWidth(header.column.columnDef, index)}
                      px={4}
                      py={5}
                      fontSize="sm"
                      fontWeight="600"
                      sx={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                      title={header.column.columnDef.header?.toString() || ''}
                    >
                      {header.isPlaceholder ? null : (
                        <Box>
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                          <chakra.span pl="4">
                            {header.column.getIsSorted() ? (
                              header.column.getIsSorted() === 'asc' ? (
                                <TriangleDownIcon aria-label="sorted descending" />
                              ) : (
                                <TriangleUpIcon aria-label="sorted ascending" />
                              )
                            ) : null}
                          </chakra.span>
                        </Box>
                      )}
                    </Th>
                  ))}
                </Tr>
              ))}
            </Thead>
            <Tbody>
              {paddingTop > 0 && (
                <tr>
                  <td
                    style={{ height: `${paddingTop}px` }}
                    colSpan={getHeaderGroups()[0]?.headers.length || 1}
                  />
                </tr>
              )}
              {virtualRows.map((virtualRow) => {
                const row = rows[virtualRow.index];
                return (
                  <Tr
                    key={row.id}
                    height={'46px'}
                    _hover={{ backgroundColor: 'gray.50' }}
                  >
                    {row.getVisibleCells().map((cell, cellIndex) => (
                      <Td
                        key={cell.id}
                        backgroundColor="inherit"
                        width={getColumnWidth(cell.column.columnDef, cellIndex)}
                        minWidth={getColumnWidth(
                          cell.column.columnDef,
                          cellIndex,
                        )}
                        px={4}
                        py={4}
                        fontSize="md"
                        lineHeight="1.4"
                        sx={{
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                        }}
                        title={String(cell.getValue() || '')}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </Td>
                    ))}
                  </Tr>
                );
              })}
              {paddingBottom > 0 && (
                <tr>
                  <td
                    style={{
                      height: `${paddingBottom}px`,
                      background: 'white',
                    }}
                    colSpan={getHeaderGroups()[0]?.headers.length || 1}
                  />
                </tr>
              )}
            </Tbody>
          </Table>
        </TableContainer>

        <Box
          width="100%"
          py={3}
          px={4}
          borderWidth="1px"
          borderTop="0"
          borderColor="gray.200"
          borderBottomRadius="md"
          position="sticky"
          bottom={0}
          bgColor={colors.white}
          zIndex={1}
        >
          <Pagination
            initialPage={currentPage}
            onChangePage={(page) => setCurrentPage(page)}
            rowsPerPage={rowsPerPage}
            totalRows={tableData?.meta.totalItems || 0}
            onChangeRowsPerPage={(rowsPerPage) => {
              setRowsPerPage(rowsPerPage);
            }}
            itemsLabel="clientes"
          />
        </Box>
      </Flex>
    </LoadingScreen>
  );
};

function areEqual(
  prevProps: DataTableServerPaginatedProps,
  nextProps: DataTableServerPaginatedProps,
): boolean {
  return (
    prevProps.headersUrl === nextProps.headersUrl &&
    prevProps.dataUrl === nextProps.dataUrl &&
    JSON.stringify(prevProps.queryParameters) ===
      JSON.stringify(nextProps.queryParameters) &&
    prevProps.showRowSelection === nextProps.showRowSelection &&
    prevProps.refetchKey === nextProps.refetchKey &&
    prevProps.rowSelection === nextProps.rowSelection &&
    JSON.stringify(prevProps.columnWidths) ===
      JSON.stringify(nextProps.columnWidths)
  );
}

export default React.memo(DataTableServerPaginated, areEqual);
