FROM node:alpine
WORKDIR /usr/src/app
COPY package*.json .
RUN npm ci
COPY . .
ARG REACT_APP_REACT_DISABLE_SSL
RUN echo "REACT_APP_REACT_DISABLE_SSL: $REACT_APP_REACT_DISABLE_SSL"

ARG REACT_APP_BACKEND_URL
RUN echo "REACT_APP_BACKEND_URL: $REACT_APP_BACKEND_URL"

ARG REACT_APP_EMAIL_UNSUBSCRIBE_URL
RUN echo "REACT_APP_EMAIL_UNSUBSCRIBE_URL: $REACT_APP_EMAIL_UNSUBSCRIBE_URL"

RUN npm run build
EXPOSE 3000
CMD [ "npx", "serve", "build" ]
